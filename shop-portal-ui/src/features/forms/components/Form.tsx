import { ReferenceDocument } from '@/features/documents/types/ReferenceDocument'
import CleaningConfirmation from '@/features/forms/templates/CleaningConfirmation'
import { Railcar } from '@/features/railcars/types/Railcar'
import { ServiceEvent } from '@/features/service-events/types/ServiceEvent'
import { FC, ReactNode } from 'react'
import { ZodSchema } from 'zod'
import { FormContent } from '../hooks/useFormDraft'
import FormProvider, { useForm } from '../providers/FormProvider'
import InboundInspection from '../templates/InboundInspection'
import type { Form, FormMRData } from '../types/Form'
import { FormTemplate } from '../types/Form'

type Props = {
  form: Form
  Schema: ZodSchema
  railcar: Railcar
  serviceEvent: ServiceEvent
  mrData: FormMRData
  content: FormContent
  completeDisabled?: boolean
  completeDisabledAlert?: ReactNode
  referenceDocuments: ReferenceDocument[]
  onContentChange?: (content: FormContent) => Promise<void>
  onComplete?: (exp: { content: FormContent; html: string }) => Promise<void>
  onSign?: (signedContent: FormContent) => Promise<void>
}

const FORM_TEMPLATE_COMPONENTS: Partial<Record<FormTemplate, FC>> = {
  [FormTemplate.CLEANING_CONFIRMATION]: CleaningConfirmation,
  [FormTemplate.INBOUND_INSPECTION]: InboundInspection,
  [FormTemplate.INBD_EXT_COATING_INSP]: InboundInspection,
  [FormTemplate.FM_AFFIRMATION]: InboundInspection,
}

const Fallback = () => {
  const form = useForm()

  return <div>Form {form.template} not Implemented</div>
}

const Form = (props: Props) => {
  const FormTemplate = FORM_TEMPLATE_COMPONENTS[props.form.template] ?? Fallback

  return (
    <FormProvider {...props}>
      <FormTemplate />
    </FormProvider>
  )
}

export default Form
