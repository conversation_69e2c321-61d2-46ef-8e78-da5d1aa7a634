import { useAuthorizedAction } from '@/app/hooks/useAuthorizedAction'
import { useRailcar } from '@/features/railcars/hooks/useRailcar'
import { useServiceEvent } from '@/features/service-events/hooks/useServiceEvent'
import { useAppContext } from '@/providers/AppProvider'
import { useMRAttributesByLocation } from '@/providers/MRAttributesProvider'
import { Permission } from '@/types/permissions'
import { i18n, useI18n } from '@gatx-corp/platform-one-common'
import { useHasPermission, useUser } from '@gatx-corp/platform-one-common/auth'
import {
  Button,
  ButtonProps,
} from '@gatx-corp/platform-one-common/components/Button'
import {
  DropDown,
  DropDownItem,
} from '@gatx-corp/platform-one-common/components/DropDown'
import { forwardRef, useMemo, useState } from 'react'
import * as Icons from 'react-bootstrap-icons'
import { archiveForm, deactivateForm, reactivateForm, resetForm } from '../api'
import useFormAction from '../hooks/useFormAction'
import useReferenceDocuments from '../hooks/useReferenceDocuments'
import { Form, FormStatusCode, getFormMRData } from '../types/Form'
import { ArchiveReasonCode, DeactivationReasonCode } from '../types/FormActions'
import { invariant } from '../utils'
import { exportForm } from '../utils/formActions'
import ArchiveFormModal from './FormActionModal/ArchiveFormModal'
import DeactivateFormModal from './FormActionModal/DeactivateFormModal'
import ResetFormModal from './FormActionModal/ResetFormModal'

// Icon names available for use in icon buttons
type IconName = keyof typeof Icons

type Props = {
  form: Form
}

type FormAction = 'deactivate' | 'archive' | 'reactivate' | 'reset'

export const ACTION_TO_LABEL: Record<FormAction, string> = {
  archive: i18n.t('ActionMenu.actions.archive', { ns: 'Forms' }),
  deactivate: i18n.t('ActionMenu.actions.deactivate', { ns: 'Forms' }),
  reactivate: i18n.t('ActionMenu.actions.reactivate', { ns: 'Forms' }),
  reset: i18n.t('ActionMenu.actions.reset', { ns: 'Forms' }),
}
export const ACTION_TO_ICON: Record<FormAction, IconName> = {
  archive: 'ArchiveFill',
  deactivate: 'DashCircleFill',
  reactivate: 'ArrowClockwise',
  reset: 'ArrowCounterclockwise',
}

export const ARCHIVE_CODE_LABELS: Record<ArchiveReasonCode, string> = {
  IO: i18n.t('ActionMenu.archive.io', { ns: 'Forms' }),
  CARRELEASED: i18n.t('ActionMenu.archive.carReleased', { ns: 'Forms' }),
  CUSTOMERCHANGE: i18n.t('ActionMenu.archive.customerChange', { ns: 'Forms' }),
  NEWASSIGN: i18n.t('ActionMenu.archive.newAssignment', { ns: 'Forms' }),
}

export const DEACTIVATION_CODE_LABELS: Record<DeactivationReasonCode, string> =
  {
    AADDERROR: i18n.t('ActionMenu.deactivation.addedInError', { ns: 'Forms' }),
    SCRAP: i18n.t('ActionMenu.deactivation.carScrapped', { ns: 'Forms' }),
    SHOPINSTRCHG: i18n.t('ActionMenu.deactivation.shopInstructionsChanged', {
      ns: 'Forms',
    }),
  }

const MENU_PERMISSIONS: Record<string, Permission> = {
  [ACTION_TO_LABEL.archive]: 'FORM_ARCHIVE',
  [ACTION_TO_LABEL.deactivate]: 'FORM_DEACTIVATE',
  [ACTION_TO_LABEL.reactivate]: 'FORM_REACTIVATE',
  [ACTION_TO_LABEL.reset]: 'FORM_RESET',
}

const ActionToggle = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'as'>>(
  function ActionToggle(props, ref) {
    return (
      <Button ref={ref} variant="neutral" {...props} icon="ThreeDotsVertical" />
    )
  },
)

const FormActionMenu = ({ form }: Props) => {
  const { t } = useI18n('Forms')
  const { serviceEventId, carNumber = '', shopCode = '' } = useAppContext()

  const [selectedAction, setSelectionAction] = useState<FormAction | null>(null)
  const [showActionModal, setShowActionModal] = useState(false)

  const { id: userId } = useUser()
  const hasPermission = useHasPermission(shopCode)

  const { data: railcar } = useRailcar({ carNumber })
  const { data: serviceEvent } = useServiceEvent({ serviceEventId })
  const referenceDocuments = useReferenceDocuments(form.id)

  const authorizedDeactivate = useAuthorizedAction(deactivateForm)
  const authorizedArchive = useAuthorizedAction(archiveForm)
  const authorizedReactivate = useAuthorizedAction(reactivateForm)
  const authorizedReset = useAuthorizedAction(resetForm)

  const mrAttrsByLocation = useMRAttributesByLocation()

  const mrData = useMemo(() => {
    if (mrAttrsByLocation) {
      return getFormMRData(form, mrAttrsByLocation)
    }

    return {}
  }, [form, mrAttrsByLocation])

  const deactivate = useFormAction(
    form.id,
    form.version,
    async function deactivate(reason: string) {
      invariant(railcar, 'Cannot export form: railcar not available')
      invariant(serviceEvent, 'Cannot export form: serviceEvent not available')
      invariant(mrData, 'Cannot export form: mrData not available')

      const html = await exportForm(
        form.id,
        railcar,
        serviceEvent,
        referenceDocuments,
        mrData,
      )
      return authorizedDeactivate(form.id, {
        payload: {
          clientVersion: process.env.NEXT_PUBLIC_CLIENT_VERSION!,
          reason,
          html,
        },
        version: form.version,
      })
    },
  )

  const reactivate = useFormAction(
    form.id,
    form.version,
    async function reactivate() {
      return authorizedReactivate(form.id, {
        version: form.version,
      })
    },
  )

  const archive = useFormAction(
    form.id,
    form.version,
    async function archive(reason: string) {
      invariant(railcar, 'Cannot export form: railcar not available')
      invariant(serviceEvent, 'Cannot export form: serviceEvent not available')
      invariant(mrData, 'Cannot export form: mrData not available')

      const html = await exportForm(
        form.id,
        railcar,
        serviceEvent,
        referenceDocuments,
        mrData,
      )
      return authorizedArchive(form.id, {
        payload: {
          clientVersion: process.env.NEXT_PUBLIC_CLIENT_VERSION!,
          userId,
          reason,
          html,
        },
        version: form.version,
      })
    },
  )

  const reset = useFormAction(form.id, form.version, async function reset() {
    return authorizedReset(form.id, {
      version: form.version,
      userId,
    })
  })

  const actionMenuItems = [
    {
      label: ACTION_TO_LABEL.archive,
      icon: ACTION_TO_ICON.archive,
      onClick: () => {
        setSelectionAction('archive')
        setShowActionModal(true)
      },
    },
    {
      label: ACTION_TO_LABEL.deactivate,
      icon: ACTION_TO_ICON.deactivate,
      onClick: () => {
        setSelectionAction('deactivate')
        setShowActionModal(true)
      },
    },
    {
      label: ACTION_TO_LABEL.reactivate,
      icon: ACTION_TO_ICON.reactivate,
      onClick: () => {
        reactivate().catch(console.error)
      },
    },
    {
      label: ACTION_TO_LABEL.reset,
      icon: ACTION_TO_ICON.reset,
      onClick: () => {
        setSelectionAction('reset')
        setShowActionModal(true)
      },
    },
  ] as DropDownItem[]

  const getMenuItemsByAction = (actions: FormAction[]): DropDownItem[] => {
    return actionMenuItems.filter((item) => {
      const menuPermission = MENU_PERMISSIONS[item.label]
      return (
        actions.map((action) => ACTION_TO_LABEL[action]).includes(item.label) &&
        (!menuPermission || hasPermission(menuPermission))
      )
    })
  }

  const getActionMenuItemsByStatus = (
    status: FormStatusCode,
  ): DropDownItem[] => {
    switch (status) {
      case FormStatusCode.SIGNED:
        return getMenuItemsByAction(['archive', 'deactivate'])
      case FormStatusCode.IN_PROCESS:
        return getMenuItemsByAction(['archive', 'deactivate', 'reset'])
      case FormStatusCode.NOT_STARTED:
        return getMenuItemsByAction(['deactivate'])
      case FormStatusCode.DEACTIVATED:
        return getMenuItemsByAction(['reactivate'])
      default:
        return []
    }
  }

  const getModalByAction = () => {
    switch (selectedAction) {
      case 'deactivate':
        return (
          <DeactivateFormModal
            showModal={showActionModal}
            onHide={() => setShowActionModal(false)}
            onDeactivate={(reason) => {
              deactivate(reason).catch(console.error)
            }}
          />
        )
      case 'archive':
        return (
          <ArchiveFormModal
            showModal={showActionModal}
            onHide={() => setShowActionModal(false)}
            onSave={(reason) => {
              archive(reason).catch(console.error)
            }}
          />
        )
      case 'reset':
        return (
          <ResetFormModal
            showModal={showActionModal}
            onHide={() => setShowActionModal(false)}
            onSave={() => {
              reset().catch(console.error)
            }}
          />
        )
      default:
        return null
    }
  }
  return (
    <>
      <DropDown.ToggleMenu
        label={t('ActionMenu.actionsForFormName', {
          formName: form ? form.shortName : 'this form',
        })}
        labelScreenreaderOnly
        Toggle={ActionToggle}
        noChevron
        disabled={form.statusCode === FormStatusCode.ARCHIVED}
        dropAlign="end"
      >
        {getActionMenuItemsByStatus(form.statusCode).map((item, index) => (
          <DropDown.Item {...item} key={index} />
        ))}
      </DropDown.ToggleMenu>
      {selectedAction && getModalByAction()}
    </>
  )
}

export default FormActionMenu
