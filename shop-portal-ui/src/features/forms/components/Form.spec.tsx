import { Railcar } from '@/features/railcars/types/Railcar'
import { ServiceEvent } from '@/features/service-events/types/ServiceEvent'
import {
  MOCK_CLEANING_FORM,
  MOCK_INBD_EXTERIOR_COATING_INSP_FORM,
  MOCK_INBOUND_INSPECTION_FORM,
  MOC<PERSON>_RAILCAR,
  MOCK_REFERENCE_DOCUMENTS,
  MOCK_SERVICE_EVENT,
} from '@/tests/mock-data'
import { render } from '@/tests/react'
import { PlatformOneProvider } from '@gatx-corp/platform-one-common'
import { getFormHTMLExport } from '../providers/FormProvider'
import {
  FormTemplate,
  getFormResponseSchema,
  getInitialFormContent,
} from '../types/Form'
import Form from './Form'

jest.mock('@/features/forms/utils/schema', () => {
  const original: Record<string, unknown> = jest.requireActual(
    '@/features/forms/utils/schema',
  )
  return {
    ...original,
    resolveSchema: jest.fn(),
  } as unknown
})

describe.each([
  [FormTemplate.CLEANING_CONFIRMATION, MOCK_CLEANING_FORM],
  [FormTemplate.INBOUND_INSPECTION, MOCK_INBOUND_INSPECTION_FORM],
  [FormTemplate.INBD_EXT_COATING_INSP, MOCK_INBD_EXTERIOR_COATING_INSP_FORM],
])('Form (%s)', (_, form) => {
  it('renders with only a top-level application provider', async () => {
    render(
      <PlatformOneProvider application="PLATFORM_ONE_SHOP_PORTAL">
        <Form
          form={form}
          content={getInitialFormContent(form, {})}
          railcar={MOCK_RAILCAR as unknown as Railcar}
          serviceEvent={MOCK_SERVICE_EVENT as unknown as ServiceEvent}
          Schema={await getFormResponseSchema(form)}
          referenceDocuments={MOCK_REFERENCE_DOCUMENTS[form.template] ?? []}
          mrData={{}}
        />
      </PlatformOneProvider>,
    )
  })

  it('getFormHTMLExport returns the html of the form', async () => {
    const html = getFormHTMLExport({
      form: form,
      content: getInitialFormContent(form, {}),
      railcar: MOCK_RAILCAR,
      serviceEvent: MOCK_SERVICE_EVENT,
      Schema: await getFormResponseSchema(form),
      referenceDocuments: MOCK_REFERENCE_DOCUMENTS[form.template] ?? [],
      mrData: {},
    })

    expect(html).toMatch(`id="current-form"`)
    expect(html).toMatch(/<!DOCTYPE html><html .*>.*<\/html>/)
    expect(html).toMatch(`data-form-title="GATX069623 - ${form.shortName}"`)
    expect(html).toMatch(`data-form-railcar="GATX069623 GAHT IB: 05/06/2024"`)
  })
})
