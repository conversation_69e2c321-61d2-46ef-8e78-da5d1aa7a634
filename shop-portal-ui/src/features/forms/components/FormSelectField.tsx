import { <PERSON><PERSON> } from '@/types/Json'
import { Size } from '@gatx-corp/platform-one-common/components/ChoiceControl'
import { FormField } from '@gatx-corp/platform-one-common/components/FormField'
import { Select } from '@gatx-corp/platform-one-common/components/Select'
import clsx from 'clsx'
import { useEffect } from 'react'
import { Option } from '../types/FormOptions'
import FormFieldContainer from './FormFieldContainer'

type Props = {
  label: string
  labelScreenreaderOnly?: boolean
  value: Json | undefined
  onChange: (value: Json) => void
  error?: string
  warning?: string
  readOnly?: boolean
  options?: Option[]
  size?: Size
  printOptions?: Option[]
  hidden?: boolean
  disabled?: boolean
  printWidth?: 'fixed' | 'full'
  help?: string
  /**
   * FIXME: This is a temporary solution while the `FormSelectField` has side-effects over the data.
   */
  noSideEffects?: boolean
}

const FormSelectField = (props: Props) => {
  const {
    label,
    labelScreenreaderOnly,
    value,
    onChange,
    error,
    warning,
    readOnly,
    options = [],
    hidden = false,
    disabled = false,
    printWidth = 'fixed',
    help,
    noSideEffects,
  } = props

  useEffect(() => {
    if (!noSideEffects && !value && options.length === 1) {
      onChange(options[0].value)
    }
  }, [value, noSideEffects, options, onChange])

  /**
   * FIXME: This is a temporary solution to account for duplicates in the provided options.
   * We might want to update the Select on @gatx-corp/platform-one-common to use index-based keys instead of the values.
   */
  const opts = options.map((option, index) => ({
    ...option,
    value: `${index}-${option.value.toString()}`,
    originalValue: option.value,
  }))

  function handleChange(value: string | undefined) {
    const opt = opts.find((o) => o.value === value)

    if (opt) {
      onChange(opt.originalValue)
    }
  }

  const currentValue = opts.find((o) => o.originalValue === value)?.value

  return (
    <FormFieldContainer
      hidden={hidden}
      screen={
        <FormField
          label={label}
          labelScreenreaderOnly={labelScreenreaderOnly}
          error={error}
          warning={warning}
          readOnly={readOnly}
          disabled={disabled}
          help={help}
        >
          <Select
            placeholder={`Select ${label}`}
            options={opts}
            value={currentValue}
            onChange={handleChange}
          />
        </FormField>
      }
      print={
        <FormField label={label} labelScreenreaderOnly={labelScreenreaderOnly}>
          <div
            className={clsx(
              'data-[empty=false]:contents data-[empty=false]:blank-print:block h-[25px] text-center rounded-lg border border-light-9',
              {
                'w-full': printWidth === 'full',
                'w-[100px]': printWidth === 'fixed',
              },
            )}
            data-empty={!value}
          >
            <span className="align-middle blank-print:hidden">
              {value as string}
            </span>
          </div>
        </FormField>
      }
    />
  )
}

export default FormSelectField
