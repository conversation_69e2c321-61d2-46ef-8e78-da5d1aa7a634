import { <PERSON><PERSON> } from '@/types/Json'
import { Size } from '@gatx-corp/platform-one-common/components/ChoiceControl'
import { FormField } from '@gatx-corp/platform-one-common/components/FormField'
import { Select } from '@gatx-corp/platform-one-common/components/Select'
import clsx from 'clsx'
import { Option } from '../types/FormOptions'
import FormFieldContainer from './FormFieldContainer'

type Props = {
  label: string
  labelScreenreaderOnly?: boolean
  value: Json | undefined
  onChange: (value: <PERSON>son) => void
  error?: string
  warning?: string
  readOnly?: boolean
  options?: Option[]
  size?: Size
  printOptions?: Option[]
  hidden?: boolean
  disabled?: boolean
  printWidth?: 'fixed' | 'full'
  help?: string
  /**
   * FIXME: This is a temporary solution while the `FormSelectField` has side-effects over the data.
   */
  noSideEffects?: boolean
}

const FormSelectField = (props: Props) => {
  const {
    label,
    labelScreenreaderOnly,
    value,
    onChange,
    error,
    warning,
    readOnly,
    options = [],
    hidden = false,
    disabled = false,
    printWidth = 'fixed',
    help,
    noSideEffects,
  } = props

  if (!noSideEffects && !value && options.length === 1) {
    onChange(options[0].value)
  }

  return (
    <FormFieldContainer
      hidden={hidden}
      screen={
        <FormField
          label={label}
          labelScreenreaderOnly={labelScreenreaderOnly}
          error={error}
          warning={warning}
          readOnly={readOnly}
          disabled={disabled}
          help={help}
        >
          <Select
            placeholder={`Select ${label}`}
            options={options}
            value={value as string | undefined}
            onChange={onChange as (v: string | undefined) => void}
          />
        </FormField>
      }
      print={
        <FormField label={label} labelScreenreaderOnly={labelScreenreaderOnly}>
          <div
            className={clsx(
              'data-[empty=false]:contents data-[empty=false]:blank-print:block h-[25px] text-center rounded-lg border border-light-9',
              {
                'w-full': printWidth === 'full',
                'w-[100px]': printWidth === 'fixed',
              },
            )}
            data-empty={!value}
          >
            <span className="align-middle blank-print:hidden">
              {value as string}
            </span>
          </div>
        </FormField>
      }
    />
  )
}

export default FormSelectField
