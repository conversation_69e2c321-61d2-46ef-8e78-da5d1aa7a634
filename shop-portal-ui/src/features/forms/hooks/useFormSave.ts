import { useAuthorizedAction } from '@/app/hooks/useAuthorizedAction'
import { saveFormContent } from '../api'
import { useForm } from '../hooks/useForm'
import { FormContent } from '../hooks/useFormDraft'
import { useCurrentFormDraft } from '../providers/FormDraftProvider'
import useFormAction from './useFormAction'

export const useFormSave = (formId: string) => {
  const [currentContent] = useCurrentFormDraft()
  const { data: form } = useForm({ formId })
  const authorizedSave = useAuthorizedAction(saveFormContent)

  return useFormAction(
    formId,
    form?.version ?? 0,
    async function save(content: FormContent | undefined = currentContent) {
      if (!form) {
        throw Error('Cannot save form: form not available')
      }

      if (!content) {
        throw Error('Cannot save form: content not available')
      }

      return authorizedSave(form, {
        content: content ?? currentContent,
        version: form.version,
      })
    },
  )
}
