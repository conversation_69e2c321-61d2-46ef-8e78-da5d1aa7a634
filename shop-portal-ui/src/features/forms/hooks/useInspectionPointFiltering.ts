import { useCallback } from 'react'
import { z } from 'zod'
import { FieldProps } from './useFormFields'

const WithInspectionPointMetadata = z.object({
  metadata: z.object({ inspectionPointId: z.number() }),
})

export const useInspectionPointFiltering = (
  fields: Partial<Record<string, FieldProps>>,
) => {
  const inspectionPointId = fields.inspectionPoint?.options?.find(
    (option) => option.value === fields.inspectionPoint?.value,
  )?.metadata.id

  const getFilteredOptions = useCallback(
    (fieldName: string) => {
      return fields[fieldName]?.options?.filter((option) => {
        const o = WithInspectionPointMetadata.safeParse(option.metadata)
        if (o.success) {
          return o.data.metadata.inspectionPointId === inspectionPointId
        }
      })
    },
    [fields, inspectionPointId],
  )

  return {
    inspectionPointId,
    getFilteredOptions,
    isInspectionPointSelected: !!fields.inspectionPoint?.value,
  }
}
