import { useMemo } from 'react'
import {
  useFormQuestionResponse,
  usePatternSchema,
} from '../providers/FormQuestionProvider'
import { getPropertyErrors } from '../utils/schema'
import { useStableArray } from './useStableArray'

type Options = {
  path?: string[]
}

/**
 * Returns the errors for the current pattern.
 * If more than one validation schema applies, the errors from each schema are merged together.
 *
 * Accepts a path to get the errors for a specific slice of the pattern.
 *
 * @param options - The options for the hook.
 * @returns The errors for the given pattern.
 */
export function useFormErrors({
  path: unstablePath = [],
}: Options = {}): Partial<Record<string, string>> {
  const path = useStableArray(unstablePath)

  const [response] = useFormQuestionResponse()
  const Schema = usePatternSchema()

  return useMemo(() => {
    if (!Schema) {
      return {}
    }

    return getPropertyErrors(Schema, path, response)
  }, [Schema, response, path])
}
