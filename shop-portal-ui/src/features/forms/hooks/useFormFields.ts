import { getValue, Json, setValue } from '@/types/Json'
import { useI18n } from '@gatx-corp/platform-one-common'
import { ZodLiteral, ZodSchema } from 'zod'
import { useForm, useFormSignatures } from '../providers/FormProvider'
import {
  useFormQuestion,
  useFormQuestionResponse,
  useFormResponseOption,
  usePatternSchema,
} from '../providers/FormQuestionProvider'
import { GLOBAL_FORM_SIGNATURE_ID } from '../types/Form'
import { FormOptions, Option } from '../types/FormOptions'
import { Remove } from '../types/FormRemove'
import {
  getConstraints,
  getPropertyPossibleKeys,
  getPropertySchema,
} from '../utils/schema'
import { useFormErrors } from './useFormErrors'

type FieldProps = {
  label: string
  value: Json | undefined
  error?: string
  readOnly?: boolean
  onChange: (value: Json) => void
  min?: number
  max?: number
  step?: number
  options?: Option[]
  printOptions?: Option[]
  hidden: boolean
  multi?: boolean
}

function isDiscriminator(
  Schema: ZodSchema,
): Schema is FormOptions | ZodLiteral<string> {
  return [ZodLiteral, FormOptions].some((T) => Schema instanceof T)
}

type Options = {
  path?: string[]
  metadata?: Record<string, string>
  signable?: boolean
}

export function useFormFields(
  options?: Options,
): Partial<Record<string, FieldProps>> {
  const { path = [], metadata = {}, signable = false } = options ?? {}
  const form = useForm()
  const { t } = useI18n('Forms')

  const [response, setResponse] = useFormQuestionResponse()

  const question = useFormQuestion()
  const option = useFormResponseOption()
  const Schema = usePatternSchema()

  const values = getValue(response, path) ?? ({} as Json)

  if (!values || typeof values !== 'object' || Array.isArray(values)) {
    throw new Error(
      `Cannot get field values for path ${path.join('.')}, property is not an object: ${JSON.stringify(values)}`,
    )
  }

  const properties = getPropertyPossibleKeys(Schema, path)

  const errors = useFormErrors({ path })

  const signatures = useFormSignatures()

  const readOnly =
    signable &&
    [path.join('.'), GLOBAL_FORM_SIGNATURE_ID, question.id.toString()].some(
      (s) => s && signatures.completed(s),
    )

  /**
   * Gets the localized string for the given path.
   * Tests different prefixes in order to find the localized string.
   *
   * @param path - The path to the property.
   * @param prefixes - The prefixes to the property.
   * @param fallback - The fallback value.
   * @returns The localized string.
   */
  function getLocalizedString<F>(
    path: string[],
    prefixes: (string | undefined)[],
    fallback: F,
  ): string | F {
    /**
     * Ignore numeric keys in the middle of the path.
     */
    const key = path
      .filter((k, idx) => isNaN(Number(k)) || idx === path.length - 1)
      .join('.')

    const strings = prefixes.map((prefix) =>
      t([prefix, key].join('.'), { ...metadata, defaultValue: '' }),
    )

    return strings.find(Boolean) ?? fallback
  }

  return Object.fromEntries(
    properties.map((key) => {
      const [PropertySchema] = getPropertySchema(
        Schema,
        [...path, key],
        response,
      )

      /**
       * Transforms the option labels to the i18n keys.
       * Looks for field-specific definitions first. If none is found, looks into the common definitions.
       */
      function optionsToI18N(options: Option[] = []) {
        return options.map((o) => ({
          label: getLocalizedString(
            [...path, key, o.label],
            [option?.pattern, form.template, 'common.options'],
            o.label,
          ),
          value: o.value,
          metadata: o.metadata,
        }))
      }

      /**
       * The blank print view needs the full set of options regardless of the form answers.
       */
      function getPrintOptions() {
        const schemas = getPropertySchema(Schema, [...path, key])

        const discriminatorSchemas = schemas.filter((S) => isDiscriminator(S))

        const { options } = getConstraints(
          FormOptions.join(discriminatorSchemas),
        )

        return optionsToI18N(options as Option[])
      }

      const constraints = getConstraints(PropertySchema)

      const label = getLocalizedString(
        [...path, key],
        [option?.pattern, form.template],
        key,
      )

      const help = getLocalizedString(
        [...path, key, 'help'],
        [option?.pattern, form.template],
        undefined,
      )

      const placeholder = getLocalizedString(
        [...path, key, 'placeholder'],
        [option?.pattern, form.template],
        undefined,
      )

      return [
        key,
        {
          ...constraints,
          options: optionsToI18N(constraints.options as Option[]),
          label: label,
          value: values[key],
          error: errors[key],
          readOnly,
          onChange(value: Json) {
            const next = setValue(response, path, { ...values, [key]: value })
            setResponse(next).catch(console.error)
          },
          hidden: !PropertySchema || PropertySchema === Remove,
          /*
           * The print view needs the full set of options regardless of the form answers.
           */
          printOptions: getPrintOptions(),
          help,
          placeholder,
        },
      ]
    }),
  )
}

export type { FieldProps }
