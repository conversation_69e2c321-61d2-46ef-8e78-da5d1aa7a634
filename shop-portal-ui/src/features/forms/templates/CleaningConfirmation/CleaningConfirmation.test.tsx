import { expirationDate } from '@/features/caching/utils/ttl'
import Form from '@/features/forms/components/Form'
import { __RAILCAR_CONFIG } from '@/features/railcars/hooks/useRailcar'
import { __SERVICE_EVENT_CONFIG } from '@/features/service-events/hooks/useServiceEvent'
import { act, render, RenderResult, useTestApp, within } from '@/tests/react'
import userEvent from '@testing-library/user-event'

import * as listActions from '@/features/forms/api/getListOptions'
import { FormContent, useFormDraft } from '@/features/forms/hooks/useFormDraft'
import { __RAILCAR_MR_ATTRIBUTES_CONFIG } from '@/features/railcars/hooks/useRailcarMRAttributes'
import { Railcar } from '@/features/railcars/types/Railcar'
import { ServiceEvent } from '@/features/service-events/types/ServiceEvent'
import {
  MOCK_CLEANING_FORM,
  MOCK_CLEANING_FORM_CONTENT,
  MOCK_RAILCAR,
  MOCK_SERVICE_EVENT,
} from '@/tests/mock-data'
import { ZodSchema } from 'zod'
import { _FORMS_CONFIG } from '../../hooks/useForm'
import { getFormResponseSchema, getInitialFormContent } from '../../types/Form'

jest.mock('@/features/forms/api/getListOptions')
jest.mock('@/features/forms/utils/schema', () => {
  const original: Record<string, unknown> = jest.requireActual(
    '@/features/forms/utils/schema',
  )
  return {
    ...original,
    resolveSchema: jest.fn(),
  } as unknown
})

afterEach(() => {
  jest.clearAllMocks()
})

const InteractiveForm = ({
  form,
  Schema,
}: {
  form: Form
  Schema: ZodSchema
}) => {
  const [content = getInitialFormContent(form, {}), setContent] = useFormDraft(
    form.id,
  )

  return (
    <Form
      form={form}
      content={content}
      onContentChange={setContent}
      railcar={MOCK_RAILCAR as unknown as Railcar}
      serviceEvent={MOCK_SERVICE_EVENT as unknown as ServiceEvent}
      Schema={Schema}
      referenceDocuments={[]}
      mrData={{}}
    />
  )
}

const StaticForm = ({
  form,
  content,
  Schema,
}: {
  form: Form
  content: FormContent
  Schema: ZodSchema
}) => {
  return (
    <Form
      form={form}
      content={content}
      railcar={MOCK_RAILCAR as unknown as Railcar}
      serviceEvent={MOCK_SERVICE_EVENT as unknown as ServiceEvent}
      Schema={Schema}
      referenceDocuments={[]}
      mrData={{}}
    />
  )
}

describe('CleaningConfirmation (Component)', () => {
  const { getRxDB, TestAppProvider } = useTestApp()

  describe('CleaningSummary', () => {
    let el: RenderResult

    beforeEach(async () => {
      await getRxDB().query_cache.insert({
        dataSet: __RAILCAR_CONFIG.dataSetName,
        queryParams: MOCK_RAILCAR.carNumber,
        response: JSON.stringify(MOCK_RAILCAR),
        expires: expirationDate(3600),
      })

      await getRxDB().query_cache.insert({
        dataSet: __SERVICE_EVENT_CONFIG.dataSetName,
        queryParams: MOCK_SERVICE_EVENT.serviceEventId,
        response: JSON.stringify(MOCK_SERVICE_EVENT),
        expires: expirationDate(3600),
      })

      jest.spyOn(listActions, 'getListOptions').mockResolvedValue({
        success: true,
        response: [],
      })
    })

    beforeEach(async () => {
      const Schema = await getFormResponseSchema(MOCK_CLEANING_FORM)

      el = await act(() =>
        render(<InteractiveForm form={MOCK_CLEANING_FORM} Schema={Schema} />, {
          wrapper: TestAppProvider,
          wrapperProps: {
            carNumber: MOCK_RAILCAR.carNumber,
            serviceEventId: MOCK_SERVICE_EVENT.serviceEventId,
            formId: MOCK_CLEANING_FORM.id,
          },
        }),
      )
    })

    it('shows the form title', () => {
      expect(el.getByText('Cleaning Confirmation')).toBeInTheDocument()
    })

    it.each([
      ['Car Number', MOCK_RAILCAR.carNumber],
      ['Facility', 'GAHT'],
      ['Capacity', '23,660 gallons (89,563 liters)'],
      ['Confirmed CIN#', '06007300 - CALCIUM CHLORIDE'],
      ['STCC#', '2812632 - CALCIUM CHLORIDE/ LIQUID'],
      ['Restriction Code', 'NR'],
      ['Car Reported Clean', 'Yes'],
      ['DNO', 'No'],
      ['DNE', 'No'],
      ['SDS', '8686'],
    ])('shows the "%s" field', (name, text) => {
      expect(el.getByRole('definition', { name })).toHaveTextContent(text)
    })
  })

  describe('CleaningInspectionSection', () => {
    let el: RenderResult
    let section: HTMLElement

    beforeEach(async () => {
      const Schema = await getFormResponseSchema(MOCK_CLEANING_FORM)

      await getRxDB().query_cache.insert({
        dataSet: _FORMS_CONFIG.dataSetName,
        queryParams: MOCK_CLEANING_FORM.id,
        response: JSON.stringify(MOCK_CLEANING_FORM),
        expires: expirationDate(3600),
      })

      await getRxDB().query_cache.insert({
        dataSet: __RAILCAR_MR_ATTRIBUTES_CONFIG.dataSetName,
        queryParams: MOCK_RAILCAR.carNumber,
        response: JSON.stringify([]),
        expires: expirationDate(3600),
      })

      el = await act(() =>
        render(<InteractiveForm form={MOCK_CLEANING_FORM} Schema={Schema} />, {
          wrapper: TestAppProvider,
          wrapperProps: {
            carNumber: MOCK_RAILCAR.carNumber,
            serviceEventId: MOCK_SERVICE_EVENT.serviceEventId,
            formId: MOCK_CLEANING_FORM.id,
          },
        }),
      )

      await userEvent.click(
        el.getByRole('button', { name: /Inspection Information/ }),
      )

      section = el.getByLabelText(/Inspection Information/)
    })

    describe('"Measurement" field', () => {
      let measurement: HTMLElement

      beforeEach(() => {
        measurement = el.getByRole('radiogroup', { name: 'Measurement' })
      })

      it('starts with "Required" warning', () => {
        expect(measurement).toHaveTextContent('Required')
      })

      it('stops being required after selecting an option', async () => {
        const inches = el.getByRole('radio', { name: 'Inches' })
        await userEvent.click(inches)
        expect(measurement).not.toHaveTextContent('Required')
      })
    })

    describe('after selecting "Inches" as "Measurement"', () => {
      it.each(['Depth of Commodity', 'Total Gallons'])(
        `shows %s`,
        async (label) => {
          const inches = el.getByRole('radio', { name: 'Inches' })
          await userEvent.click(inches)

          const field = within(section).getByRole('textbox', {
            name: new RegExp(label),
          })

          expect(field).toBeVisible()
        },
      )
    })

    describe.each(['Film', 'N/A'])(
      'after selecting %s as "Measurement"',
      (measurement) => {
        it.each(['Depth of Commodity', 'Total Gallons'])(
          `does not show %s"`,
          async (label) => {
            const group = el.getByRole('radiogroup', { name: 'Measurement' })
            const input = within(group).getByRole('radio', {
              name: measurement,
            })

            await userEvent.click(input)

            const field = el.queryByRole('textbox', { name: new RegExp(label) })

            expect(field).toBeNull()
          },
        )
      },
    )
  })

  describe('QualityAssurance', () => {
    let el: RenderResult
    let baseContent: FormContent
    const WARNING_INDICATES_TEXT =
      'Recipe indicates Nitrogen was used. Nitrogen Warning Tag expected unless car has been vented with air'
    const WARNING_DOES_NOT_INDICATE_TEXT =
      'Recipe does not indicate Nitrogen was used. Nitrogen Warning Tag not expected'

    const renderFormWithContent = async (updatedContent: FormContent) => {
      const Schema = await getFormResponseSchema(MOCK_CLEANING_FORM)

      return await act(() =>
        render(
          <StaticForm
            form={MOCK_CLEANING_FORM}
            content={updatedContent}
            Schema={Schema}
          />,
          {
            wrapper: TestAppProvider,
            wrapperProps: {
              carNumber: MOCK_RAILCAR.carNumber,
              serviceEventId: MOCK_SERVICE_EVENT.serviceEventId,
              formId: MOCK_CLEANING_FORM.id,
            },
          },
        ),
      )
    }

    it('no warning should be displayed when "Nitrogen Warning Tag Applied" is "Yes" and cleaning methods includes "Nitrogen" and "Air"', async () => {
      baseContent = FormContent.parse(MOCK_CLEANING_FORM_CONTENT)
      baseContent.response['780'] = {
        data: {
          cleaningInformation: { cleaningMethods: ['Air', 'Nitrogen'] },
          cleaningQA: { NiApplied: 'Yes' },
        },
        questionId: 780,
        responseId: 99,
        comments: [],
      }

      el = await renderFormWithContent(baseContent)

      expect(
        el.queryByText(WARNING_DOES_NOT_INDICATE_TEXT),
      ).not.toBeInTheDocument()

      expect(el.queryByText(WARNING_INDICATES_TEXT)).not.toBeInTheDocument()
    })

    it('no warning should be displayed when "Nitrogen Warning Tag Applied" is "No" and cleaning methods includes "Nitrogen" and "Air"', async () => {
      baseContent = FormContent.parse(MOCK_CLEANING_FORM_CONTENT)
      baseContent.response['780'] = {
        data: {
          cleaningInformation: { cleaningMethods: ['Air', 'Nitrogen'] },
          cleaningQA: { NiApplied: 'No' },
        },
        questionId: 780,
        responseId: 99,
        comments: [],
      }

      el = await renderFormWithContent(baseContent)

      expect(
        el.queryByText(WARNING_DOES_NOT_INDICATE_TEXT),
      ).not.toBeInTheDocument()

      expect(el.queryByText(WARNING_INDICATES_TEXT)).not.toBeInTheDocument()
    })

    it('displays a warning when "Nitrogen Warning Tag Applied" is "Yes" but cleaning methods does not include "Nitrogen" and "Air"', async () => {
      baseContent = FormContent.parse(MOCK_CLEANING_FORM_CONTENT)
      baseContent.response['780'] = {
        data: {
          cleaningInformation: { cleaningMethods: ['Water'] },
          cleaningQA: { NiApplied: 'Yes' },
        },
        questionId: 780,
        responseId: 99,
        comments: [],
      }

      el = await renderFormWithContent(baseContent)

      expect(el.getByText(WARNING_DOES_NOT_INDICATE_TEXT)).toBeInTheDocument()
    })

    it('displays a warning when "Nitrogen Warning Tag Applied" is "No" but cleaning methods includes "Nitrogen" but not "Air"', async () => {
      baseContent = FormContent.parse(MOCK_CLEANING_FORM_CONTENT)
      baseContent.response['780'] = {
        data: {
          cleaningInformation: { cleaningMethods: ['Nitrogen'] },
          cleaningQA: { NiApplied: 'No' },
        },
        questionId: 780,
        responseId: 99,
        comments: [],
      }

      el = await renderFormWithContent(baseContent)

      expect(el.getByText(WARNING_INDICATES_TEXT)).toBeInTheDocument()
    })
  })
})
