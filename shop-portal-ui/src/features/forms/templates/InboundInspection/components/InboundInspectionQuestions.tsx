import FormQuestion from '@/features/forms/components/FormQuestion'
import { FormQuestionHeader } from '@/features/forms/components/FormQuestionHeader'
import {
  sign,
  useForm,
  useFormSignatures,
  useGetFormQuestionStatus,
  useHiddenQuestionIds,
  useQuestionHandle,
  useSetFormContent,
} from '@/features/forms/providers/FormProvider'
import { FormContent } from '@/features/forms/types/Form'
import { FormResponsePattern } from '@/features/forms/types/FormResponsePattern'
import { i18n, useI18n } from '@gatx-corp/platform-one-common'
import { useUser } from '@gatx-corp/platform-one-common/auth'
import { Accordion } from '@gatx-corp/platform-one-common/components/Accordion'
import { useEffect, useRef } from 'react'
import { Trans } from 'react-i18next'
import QuestionSignature from './QuestionSignature'
import QuestionStatus from './QuestionStatus'

const InboundInspectionQuestions = () => {
  const { t } = useI18n('Forms', { keyPrefix: 'Inbound Inspection' })
  const form = useForm()
  const signatures = useFormSignatures()
  const setContent = useSetFormContent()
  const hiddenQuestionIds = useHiddenQuestionIds()
  const user = useUser()
  const accordion = useQuestionHandle()
  const hiddenQuestionsAmount = useRef<number>(-1)
  const getStatus = useGetFormQuestionStatus()

  function expandNextQuestion(questionId: number) {
    const questionIndex = form.questions.findIndex((q) => q.id === questionId)

    if (questionIndex === -1) return

    for (let i = questionIndex + 1; i < form.questions.length; i++) {
      if (!hiddenQuestionIds.has(form.questions[i].id)) {
        accordion?.current?.expand(i)
        return
      }
    }
  }

  const onPatternChange = (
    question: FormQuestion,
    pattern: FormResponsePattern | null,
    response: FormContent['response'],
  ) => {
    if (getStatus(question, response) !== 'valid') {
      return
    }

    const questionId = question.id.toString()

    if (
      pattern === FormResponsePattern.NONE &&
      !signatures.get(questionId) &&
      signatures.signable(questionId)
    ) {
      /**
       * FIXME: cannot use signature.sign because it would override the response change that emits this "PatternChange" event.
       *
       * State management (current via RxDB) must refactored in a way that allows batched updates to the form's content. I.e.: using a Reducer.
       */
      setContent((prev) => ({
        ...prev,
        signatures: sign(`${question.id}`, user, 'complete', prev.signatures),
        response,
      })).catch((e) =>
        console.error(`Could not set content for question ${questionId}: `, e),
      )

      expandNextQuestion(question.id)
    }
  }

  useEffect(() => {
    function shouldExpandFirstQuestion(): boolean {
      const currentHiddenCount = hiddenQuestionIds.size
      const initialHiddenCount = hiddenQuestionsAmount.current
      // filter selected is 'all' or hiddenQuestionsIds is not loaded
      if (currentHiddenCount === 0) return false
      // amount of hidden questions increased so next question should be expanded instead
      if (initialHiddenCount !== -1 && currentHiddenCount >= initialHiddenCount)
        return false
      return true
    }
    if (shouldExpandFirstQuestion()) {
      hiddenQuestionsAmount.current = hiddenQuestionIds.size
      const firstVisibleQuestionId = form.questions.findIndex(
        (q) => !hiddenQuestionIds.has(q.id),
      )
      if (firstVisibleQuestionId !== -1)
        accordion?.current?.expand(firstVisibleQuestionId)
    }
  }, [hiddenQuestionIds, form, accordion])

  const empty = hiddenQuestionIds.size === form.questions.length

  return (
    <>
      <div
        className="contents data-[hidden=true]:hidden print:data-[hidden=true]:contents"
        aria-hidden={empty}
        data-hidden={empty}
      >
        <Accordion.Container
          variant="card"
          hasExpandCollapse
          screenReaderContext={t('sectionsTitle')}
          expandFirstItem
          handle={accordion}
        >
          {form.questions.map((question) => (
            <Accordion.Item
              key={question.id}
              title={question.question}
              indicators={<QuestionStatus question={question} />}
              className="data-[hidden=true]:hidden print:data-[hidden=true]:contents relative break-inside-avoid"
              aria-hidden={hiddenQuestionIds.has(question.id)}
              data-hidden={hiddenQuestionIds.has(question.id)}
            >
              <div className="customer-print:flex gap-lg print:gap-0 px-md pt-md pb-0 flex flex-col print:p-0">
                <FormQuestionHeader
                  question={question}
                  formTemplateName={form.template}
                />
                <FormQuestion
                  question={question}
                  onPatternChange={(p, r) => onPatternChange(question, p, r)}
                />
                <QuestionSignature
                  question={question}
                  onSign={() => expandNextQuestion(question.id)}
                />
              </div>
            </Accordion.Item>
          ))}
        </Accordion.Container>
      </div>
      <div
        className="flex flex-col items-center justify-center h-full gap-sm px-lg py-2xl print:hidden data-[hidden=true]:hidden"
        aria-hidden={!empty}
        data-hidden={!empty}
      >
        <Trans
          i18n={i18n}
          i18nKey="Inbound Inspection.filter.empty"
          ns="Forms"
          components={{
            title: <div className="text-title-rg" />,
            description: <div className="text-body-rg" />,
          }}
        />
      </div>
    </>
  )
}

export default InboundInspectionQuestions
