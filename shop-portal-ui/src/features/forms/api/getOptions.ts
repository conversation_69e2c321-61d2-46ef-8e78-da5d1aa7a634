import { performCachingQuery } from '@/features/caching/utils'
import { HOUR_SECONDS } from '@/features/caching/utils/ttl'
import { getListOptions } from '../api/getListOptions'

async function getOptionsServer(query?: Record<string, unknown>) {
  const result = await getListOptions(query ?? {})
  if (result.success) {
    return result.response
  }

  return []
}

async function getOptionsClient(query?: Record<string, unknown>) {
  const { name, ...params } = query ?? {}
  const cacheKey = { name, params }
  const queryId = encodeURIComponent(JSON.stringify(cacheKey))

  // TODO: review updating the Caching layer
  // to support having the QueryParams as an object and not a single string
  const doQuery = () => {
    return performCachingQuery(queryId, {
      dataSetName: 'listOptions',
      remoteQueryFunction: () => getListOptions(query ?? {}),
      timeToLive: HOUR_SECONDS,
    })
  }

  const { data: opts = [] } = await doQuery()
  return opts
}

const getOptions = async (query?: Record<string, unknown>) => {
  const getOpts = () =>
    typeof window === 'undefined'
      ? getOptionsServer(query)
      : getOptionsClient(query)

  const opts = await getOpts()
  return opts.map((o) => ({
    label: o.label,
    value: o.value,
    metadata: o,
  }))
}

export { getOptions }
