import { i18n } from '@gatx-corp/platform-one-common'
import { SetStateAction } from 'react'
import { ZodSchema } from 'zod'
import { Form, FormQuestionResponse, FormStatusCode } from '../types/Form'

/**
 * Check if a question response is valid
 * @param response - The question response
 * @param schema - The schema to validate the response against
 * @returns True if the response is valid, false otherwise
 */
export function isQuestionValid(
  response: FormQuestionResponse,
  schema: ZodSchema,
): boolean {
  const result = schema.safeParse(response.data)
  return result.success
}

export const booleanToYesNo = (value: boolean): string =>
  value ? i18n.t('CommonUI:yes') : i18n.t('CommonUI:no')

/**
 * Checks if the form is editable based on its status.
 * @param formStatusCode
 * @returns true if the form is editable, false otherwise
 */

export function isFormEditable(form: Form): boolean {
  return (
    form.statusCode === FormStatusCode.NOT_STARTED ||
    form.statusCode === FormStatusCode.IN_PROCESS
  )
}

export function invariant<T>(condition: T, message: string): asserts condition {
  if (!condition) {
    throw new Error(message)
  }
}

export type StateDuple<T> = [T, (data: SetStateAction<T>) => Promise<void>]
