import { get<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@/types/Json'
import {
  z,
  <PERSON>od<PERSON><PERSON><PERSON>,
  ZodBigInt,
  ZodDate,
  ZodDefault,
  ZodDiscriminatedUnion,
  ZodError,
  ZodFormattedError,
  ZodIntersection,
  ZodNumber,
  ZodObject,
  ZodRawShape,
  ZodSchema,
  ZodString,
  ZodTypeAny,
  ZodUnion,
} from 'zod'
import { FormBranch, merge } from '../types/FormBranch'
import { FormOptions } from '../types/FormOptions'
import { Remove } from '../types/FormRemove'

type ObjectSchema = ZodObject<ZodRawShape>
type UnionSchema<T extends ZodSchema> = ZodUnion<[T, T, ...T[]]>
type ObjectUnionSchema = UnionSchema<ObjectSchema>

function isObjectUnionSchema(u: unknown): u is ObjectUnionSchema {
  return (
    (u instanceof ZodUnion || u instanceof ZodDiscriminatedUnion) &&
    Array.isArray(u.options) &&
    u.options.every((S) => S instanceof ZodObject)
  )
}

function isObjectBranch(u: unknown): u is FormBranch {
  return u instanceof FormBranch
}

function isZodDefault(u: unknown): u is ZodDefault<ZodSchema> {
  return u instanceof ZodDefault
}

function isObjectIntersection(
  u: unknown,
): u is ZodIntersection<ObjectSchema, ObjectSchema> {
  return (
    u instanceof ZodIntersection &&
    u._def.left instanceof ZodObject &&
    u._def.right instanceof ZodObject
  )
}

function isObjectSchema(u: unknown): u is ObjectSchema {
  return u instanceof ZodObject
}

function isArraySchema(u: unknown): u is ZodArray<ObjectSchema> {
  return u instanceof ZodArray
}

/**
 * Returns an array of potential schemas for a given property path within a schema.
 *
 * When traversing complex schemas that include union types (e.g., ZodUnion, ZodDiscriminatedUnion, FormBranch),
 * ambiguities can arise. This function handles such cases by returning a distinct schema for each branch of the union.
 * Supplying an input object that provides a value for the specified path can help narrow the ambiguity; if the input
 * is insufficient to determine a unique branch, the full list of possible schemas is returned.
 *
 * If the schema does not include union types along the path, the returned array contains a single schema.
 *
 * If the path terminates at the discriminator of a FormBranch, a single consolidated FormOptions schema is returned,
 * aggregating one option per branch of the FormBranch.
 *
 * When the path traverses only FormBranch union types, the input object may be used to resolve the ambiguity.
 * In this scenario, if the input validates against exactly one branch, only that schema is returned; if it validates
 * against none, an empty array is returned.
 *
 * @param Schema The schema to traverse.
 * @param path The property path to resolve.
 * @param input (Optional) An input object used to disambiguate the schema based on the value at the specified path.
 * @returns An array of schemas corresponding to the property at the end of the path.
 */
function getPropertySchema(
  Schema: ZodSchema,
  path: readonly string[],
  input?: unknown,
): ZodSchema[] {
  if (!path.length) {
    return [Schema]
  }

  if (isZodDefault(Schema)) {
    return getPropertySchema(Schema._def.innerType, path, input)
  }

  if (isObjectSchema(Schema)) {
    const [key, ...rest] = path

    if (!(key in Schema.shape)) {
      return []
    }

    if (!rest.length) {
      return [Schema.shape[key] as ZodSchema]
    }

    return getPropertySchema(
      Schema.shape[key] as ZodSchema,
      rest,
      getValue(input as Json, [key]),
    )
  }

  if (isObjectUnionSchema(Schema)) {
    return Schema.options.flatMap((S) => getPropertySchema(S, path, input))
  }

  if (isObjectIntersection(Schema)) {
    const S = Schema._def.left.merge(Schema._def.right)
    return getPropertySchema(S, path, input)
  }

  if (isObjectBranch(Schema)) {
    return Schema.getPropertySchema(path, input)
  }

  if (isArraySchema(Schema)) {
    const [key, ...rest] = path

    return getPropertySchema(
      Schema.element,
      rest,
      getValue(input as Json, [key]),
    )
  }

  return []
}

function getPossibleKeys(Schema: ZodSchema): string[] {
  let keys: string[] = []

  if (isZodDefault(Schema)) {
    keys = getPossibleKeys(Schema._def.innerType)
  }

  if (isObjectSchema(Schema)) {
    keys = Object.keys(Schema.shape)
  }

  if (isObjectUnionSchema(Schema)) {
    keys = Schema.options.flatMap((S) => getPossibleKeys(S))
  }

  if (isObjectIntersection(Schema)) {
    const S = Schema._def.left.merge(Schema._def.right)
    return getPossibleKeys(S)
  }

  if (isObjectBranch(Schema)) {
    keys = Schema.options().flatMap((S) => getPossibleKeys(S as ZodSchema))
  }

  if (isArraySchema(Schema)) {
    return getPossibleKeys(Schema.element)
  }

  return [...new Set(keys)]
}

function getPropertyPossibleKeys(Schema: ZodSchema, path: string[]): string[] {
  if (path.length === 0) {
    return getPossibleKeys(Schema)
  }

  return [...new Set(getPropertySchema(Schema, path).flatMap(getPossibleKeys))]
}

type Constrainable = ZodString | ZodNumber | ZodBigInt | ZodDate | FormOptions

function isConstrainable(u: unknown): u is Constrainable {
  return [ZodString, ZodNumber, ZodBigInt, ZodDate, FormOptions].some(
    (T) => u instanceof T,
  )
}

function getConstrainableSchema(schema: ZodSchema): Constrainable | null {
  if (isConstrainable(schema)) {
    return schema
  } else if ('innerType' in schema._def) {
    return getConstrainableSchema(schema._def.innerType as ZodSchema)
  } else {
    return null
  }
}

function getConstraints(Schema?: ZodSchema): Record<string, unknown> {
  if (!Schema) return {}

  const ConstrainableSchema = getConstrainableSchema(Schema)

  if (ConstrainableSchema) {
    const constraints: Record<string, unknown> = {}
    if ('checks' in ConstrainableSchema._def) {
      ConstrainableSchema._def.checks.forEach((check) => {
        if (check.kind === 'min') constraints.min = check.value
        if (check.kind === 'max') constraints.max = check.value
        if (check.kind === 'multipleOf') constraints.step = check.value
      })
    }

    if (ConstrainableSchema instanceof FormOptions) {
      constraints.options = ConstrainableSchema._def.options
      constraints.multi = ConstrainableSchema._def.multi
    }

    return constraints
  }

  return {}
}

// TODO: consider new clone api in Zod v4
function cloneSchema(Schema: ZodSchema): ZodSchema {
  if (isZodDefault(Schema)) {
    return cloneSchema(Schema._def.innerType).default(Schema._def.defaultValue)
  }

  if (isObjectSchema(Schema)) {
    return z.object(
      Object.fromEntries(
        Object.entries(Schema.shape).map(([key, S]) => [
          key,
          cloneSchema(S as ZodSchema),
        ]),
      ),
    )
  }

  if (isObjectUnionSchema(Schema)) {
    // TODO: support z.discriminatedUnion
    return z.union(
      Schema.options.map((S) => cloneSchema(S)) as [
        ZodTypeAny,
        ZodTypeAny,
        ...ZodTypeAny[],
      ],
    )
  }

  if (isObjectBranch(Schema)) {
    return Schema.clone()
  }

  if (Schema instanceof FormOptions) {
    return Schema.clone()
  }

  if (isArraySchema(Schema)) {
    return z.array(cloneSchema(Schema.element))
  }

  // TODO: support cloning other types
  return Schema
}

function resolveSchemaTraverse(
  Schema: ZodSchema,
  query: Record<string, unknown>,
): Promise<void>[] {
  const promises: Promise<void>[] = []

  if (isObjectSchema(Schema)) {
    Object.values(Schema.shape).forEach((S: ZodSchema) =>
      promises.push(...resolveSchemaTraverse(S, query)),
    )
  }

  if (isObjectUnionSchema(Schema)) {
    Object.values(Schema.options).forEach((S: ZodSchema) =>
      promises.push(...resolveSchemaTraverse(S, query)),
    )
  }

  if (isObjectBranch(Schema)) {
    Schema.options().forEach((S: ZodSchema) =>
      promises.push(...resolveSchemaTraverse(S, query)),
    )
  }

  if (Schema instanceof FormOptions) {
    return [Schema.resolve(query)]
  }

  if (isArraySchema(Schema)) {
    return resolveSchemaTraverse(Schema.element, query)
  }

  return promises
}

function resolveSchema(
  Schema: ZodSchema,
  query: Record<string, unknown> = {},
): Promise<void[]> {
  return Promise.all(resolveSchemaTraverse(Schema, query))
}

function isStringArray(u: unknown): u is string[] {
  return Array.isArray(u) && u.every((s) => typeof s === 'string')
}

const Wrapper = z.object({
  _def: z.object({ innerType: z.instanceof(ZodSchema) }),
})

function unwrap(Schema: ZodTypeAny): ZodTypeAny {
  const res = Wrapper.safeParse(Schema)

  if (res.success) {
    return unwrap(res.data._def.innerType)
  }

  return Schema
}

/**
 * Parses an object using the given Schema.
 * Does not throw if the object is invalid. Instead, only the valid properties are returned.
 *
 * @param Schema The schema to use.
 * @param input The data to parse.
 * @returns The parsed data.
 */
function parseValid(Schema: ZodTypeAny, input: unknown): unknown {
  const res = Schema.safeParse(input)

  if (res.success) {
    return res.data
  }

  const error = res.error.format()

  const Unwrapped = unwrap(Schema)

  if (isObjectSchema(Unwrapped) && typeof input === 'object' && input) {
    const out: Record<string, unknown> = {}

    type Key = keyof typeof input

    for (const [key, S] of Object.entries(Unwrapped.shape) as [
      Key,
      ZodSchema,
    ][]) {
      if (key in input) {
        let r: unknown

        if (key in error) {
          r = parseValid(S, input[key])
        } else {
          r = S.parse(input[key]) as unknown
        }

        if (r !== undefined) {
          out[key] = r
        }
      }
    }

    return out
  }

  if (isObjectBranch(Unwrapped)) {
    return Unwrapped.parseValid(input)
  }

  if (isObjectUnionSchema(Unwrapped)) {
    return Unwrapped.options.reduce(
      (acc, S) => ({ ...acc, ...(parseValid(S, input) ?? {}) }),
      {} as Record<string, unknown>,
    )
  }

  if (isArraySchema(Unwrapped) && Array.isArray(input)) {
    return input.map((item) => parseValid(Unwrapped.element, item))
  }

  return undefined
}

/**
 * Checks if the property in the input is valid for the given schema.
 *
 * Depending on the schema, a given property could have many possible schemas.
 * For example, in a union the property may have as many schemas as there are options in the union.
 *
 * This function checks if the property in the input is valid for all of the possible schemas.
 * The possible schemas are narrowed down by the given input, so FormBranch schemas will be resolved during this evaluation.
 *
 * @param Schema The schema to check.
 * @param path The path to the property.
 * @param input The input to check.
 * @returns True if the property is valid, false otherwise.
 */
function isPropertyValid(
  Schema: ZodSchema,
  path: string[],
  input: unknown,
): boolean {
  const json = Json.safeParse(input)

  if (!json.success) {
    return false
  }

  /**
   * These are the possible schemas for the property, narrowed down by the given input.
   * The property in the input is valid if it validates against all of the possible schemas.
   */
  return getPropertySchema(Schema, path, json.data).every(
    (S) => S.safeParse(getValue(json.data, path)).success,
  )
}

const ErrorRecord = z.record(z.object({ _errors: z.string().array() }))

function omit(obj: Record<string, unknown>, key: string) {
  return Object.fromEntries(Object.entries(obj).filter(([k]) => k !== key))
}

function map<T, R>(
  obj: Record<string, T>,
  fn: (value: T) => R,
): Record<string, R> {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [key, fn(value)]),
  )
}

export function getFormattedErrors(
  error: ZodError<unknown>,
): ZodFormattedError<unknown>[] {
  let errors: ZodFormattedError<unknown>[]

  if (error.issues.length === 1 && error.issues[0].code === 'invalid_union') {
    errors = error.issues[0].unionErrors.map((e) => e.format())
  } else {
    errors = [error.format()]
  }

  return errors
}

function getErrors(Schema: ZodSchema, path: string[], input: unknown) {
  const json = Json.safeParse(input)

  if (!json.success) {
    return []
  }

  const result = Schema.safeParse(getValue(json.data, path) ?? {})
  return result.success ? [] : getFormattedErrors(result.error)
}

type Path<T> = T extends object
  ? { [K in keyof T]: readonly [K, ...Path<T[K]>] }[keyof T] | readonly []
  : readonly []

type TypeAtPath<T, P extends readonly string[]> = P extends []
  ? T
  : P extends readonly [infer First, ...infer Rest extends readonly string[]]
    ? First extends keyof T
      ? TypeAtPath<T[First], Rest>
      : never
    : never

/**
 * Returns the errors for a given property path within a schema.
 * If more than one validation schema applies, the errors from each schema are merged together.
 *
 * @param Schema The schema to traverse.
 * @param path The path to the property.
 * @param input The input to check.
 * @returns The errors for the given property path.
 */
function getPropertyErrors<S extends ZodTypeAny, P extends Path<z.infer<S>>>(
  Schema: S,
  path: Readonly<P>,
  input: unknown,
): Partial<Record<keyof TypeAtPath<z.infer<S>, P>, string>> {
  const sectionSchemas = getPropertySchema(
    Schema,
    path as unknown as string[],
    input,
  )

  const errors = sectionSchemas.flatMap((schema) =>
    getErrors(schema, path as unknown as string[], input),
  )

  if (errors.length === 0) return {}

  const mergedErrors = errors
    .map((e) => omit(e, '_errors'))
    .reduce((acc, curr) => ({ ...acc, ...curr }), {})

  return map(
    ErrorRecord.parse(mergedErrors),
    ({ _errors: [error] }) => error,
  ) as Partial<Record<keyof TypeAtPath<z.infer<S>, P>, string>>
}

type Merge<A, B> = A extends Remove
  ? A | Remove
  : B extends Remove
    ? B | Remove
    : A & B

type MergeCommonKeys<T extends ZodRawShape, U extends ZodRawShape> = {
  [K in keyof T | keyof U]: K extends keyof T
    ? K extends keyof U
      ? Merge<T[K], U[K]>
      : T[K]
    : K extends keyof U
      ? U[K]
      : never
}

/**
 * Merges two base Zod objects.
 *
 * This function takes two base Zod object schema and merges their shapes.
 *
 * It computes the union of keys from both schemas and merges their properties according to the following rules:
 * - If a key exists in both schemas, the properties are merged.
 * - If a key exists in only one schema, that property is retained.
 * - If a key exists in one schema and is marked as `Remove` in the other, it is removed.
 *
 * The resulting schema is a new Zod object that combines the shapes of both input schemas.
 *
 *
 * @param base - The base ZodObject schema to extend.
 * @param S - The ZodObject schema to merge with the base schema.
 *
 * @returns A new ZodObject with schema options extended by the base schema.
 *
 */

function mergeObjectSchemas<E extends ZodRawShape, S extends ZodRawShape>(
  Base: ZodObject<E>,
  S: ZodObject<S>,
): ZodObject<MergeCommonKeys<E, S>> {
  const commmonKeys = Array.from(
    new Set([...Object.keys(S.shape), ...Object.keys(Base.shape)]),
  )

  const shape = Object.fromEntries(
    commmonKeys.map((key): [string, ZodTypeAny] => {
      const S1 = Base.shape[key]
      const S2 = S.shape[key]

      if (isObjectBranch(S1) && isObjectSchema(S2)) {
        return [key, merge(S2, S1)]
      }

      if (isObjectBranch(S2) && isObjectSchema(S1)) {
        return [key, merge(S1, S2)]
      }

      if (isObjectSchema(S1) && isObjectSchema(S2)) {
        return [key, mergeObjectSchemas(S1, S2)]
      }

      if (isObjectSchema(S1) && S2 !== Remove) {
        return [key, S1]
      }

      if (isObjectSchema(S2)) {
        return [key, S2]
      }

      if (S1 && !S2) {
        return [key, S1]
      }

      if (S2 && !S1) {
        return [key, S2]
      }

      if (S1 && S2 !== Remove) {
        return [key, S1]
      }

      return [key, Remove]
    }),
  )

  return S.merge(Base).extend(shape) as unknown as ZodObject<
    MergeCommonKeys<E, S>
  >
}

export {
  cloneSchema,
  getConstraints,
  getPossibleKeys,
  getPropertyErrors,
  getPropertyPossibleKeys,
  getPropertySchema,
  isArraySchema,
  isObjectBranch,
  isObjectSchema,
  isObjectUnionSchema,
  isPropertyValid,
  isStringArray,
  isZodDefault,
  mergeObjectSchemas,
  parseValid,
  resolveSchema,
}

export type { MergeCommonKeys, ObjectSchema, Path }
