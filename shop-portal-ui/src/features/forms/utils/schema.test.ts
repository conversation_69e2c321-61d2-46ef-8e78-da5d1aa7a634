import {
  z,
  ZodBoolean,
  ZodLiteral,
  ZodNumber,
  ZodObject,
  ZodSchema,
  ZodString,
} from 'zod'
import branch from '../types/FormBranch'
import number from '../types/FormNumber'
import options, { FormOptions } from '../types/FormOptions'
import {
  getConstraints,
  getPossibleKeys,
  getPropertyErrors,
  getPropertyPossibleKeys,
  getPropertySchema,
  isArraySchema,
  isObjectSchema,
  isObjectUnionSchema,
  isPropertyValid,
  mergeObjectSchemas,
  parseValid,
  resolveSchema,
} from './schema'

describe('Schema Module', () => {
  describe('isObjectSchema', () => {
    it('returns true if the input describes an object', () => {
      expect(isObjectSchema(z.object({}))).toBe(true)
    })

    it('returns false if the input does not describe an object', () => {
      expect(isObjectSchema(z.string())).toBe(false)
      expect(isObjectSchema(z.union([z.string(), z.number()]))).toBe(false)
      expect(isObjectSchema(z.literal('foo'))).toBe(false)
    })
  })

  describe('isObjectUnionSchema', () => {
    it('returns true if the input describes a union of objects', () => {
      const schema = z.union([z.object({}), z.object({ foo: z.string() })])
      expect(isObjectUnionSchema(schema)).toBe(true)
      expect(isObjectUnionSchema(z.object({}).or(z.object({})))).toBe(true)
    })

    it('returns true if the input describes a discriminated union', () => {
      const schema = z.discriminatedUnion('type', [
        z.object({ type: z.literal('A'), foo: z.string() }),
        z.object({ type: z.literal('B'), bar: z.number() }),
      ])
      expect(isObjectUnionSchema(schema)).toBe(true)
    })

    it('returns false if the input does not describe a union of objects', () => {
      expect(isObjectUnionSchema(z.object({}))).toBe(false)
      expect(isObjectUnionSchema(z.string())).toBe(false)
      expect(isObjectUnionSchema(z.union([z.string(), z.number()]))).toBe(false)
      expect(isObjectUnionSchema(z.literal('foo'))).toBe(false)
      expect(isObjectUnionSchema(z.object({}).or(z.string()))).toBe(false)
    })
  })

  describe('isArraySchema', () => {
    it('returns true if the input describes an array', () => {
      expect(isArraySchema(z.object({}).array())).toBe(true)
    })

    it('returns false if the input does not describe an array', () => {
      expect(isArraySchema(z.object({}))).toBe(false)
      expect(isArraySchema(z.string())).toBe(false)
      expect(isArraySchema(z.union([z.string(), z.number()]))).toBe(false)
      expect(isArraySchema(z.literal('foo'))).toBe(false)
    })
  })

  describe('getPropertySchema', () => {
    describe('when path has a single key', () => {
      it('returns a single sub schema if the schema describes an object', () => {
        const Schema = z.object({ foo: z.string() })

        expect(getPropertySchema(Schema, ['foo'])).toEqual([Schema.shape.foo])
      })

      it('returns a single sub schema if the schema describes an array', () => {
        const Schema = z.object({}).array()

        expect(getPropertySchema(Schema, ['0'])).toEqual([Schema.element])
      })

      it('returns empty array if the key is not present in the object schema', () => {
        const Schema = z.object({ foo: z.string() })

        expect(getPropertySchema(Schema, ['bar'])).toEqual([])
      })

      it('returns an array of sub schemas if the schema describes a union of objects', () => {
        const Schema = z.union([
          z.object({ foo: z.string() }),
          z.object({ foo: z.number() }),
        ])

        const [first, second] = Schema.options.map((s) => s.shape.foo)

        expect(getPropertySchema(Schema, ['foo'])).toEqual([first, second])
      })

      it('returns a single sub schema if the key is present in only one of the object schemas', () => {
        const Schema = z.union([
          z.object({ foo: z.string() }),
          z.object({ bar: z.number() }),
        ])

        const [first, second] = Schema.options

        expect(getPropertySchema(Schema, ['foo'])).toEqual([first.shape.foo])
        expect(getPropertySchema(Schema, ['bar'])).toEqual([second.shape.bar])
      })

      it('returns empty array if the key is not present in any of the object schemas', () => {
        const Schema = z.union([
          z.object({ foo: z.string() }),
          z.object({ bar: z.number() }),
        ])

        expect(getPropertySchema(Schema, ['baz'])).toEqual([])
      })

      it('returns a joint FormOptions schema if the key is a discriminator path in a FormBranch', () => {
        const Schema = branch(
          ['type'],
          z.object({ type: z.literal('a'), foo: z.string() }),
          z.object({ type: z.literal('b'), bar: z.number() }),
        )

        const [Result, ...rest] = getPropertySchema(Schema, ['type']) as [
          FormOptions,
        ]

        expect(rest).toEqual([])
        expect(Result).toBeInstanceOf(FormOptions)
        expect(Result._def.options).toEqual([
          { label: 'A', value: 'a', metadata: {} },
          { label: 'B', value: 'b', metadata: {} },
        ])
      })

      it('returns multiple sub schemas if the key is not a discriminator path in a FormBranch', () => {
        const Schema = branch(
          ['type'],
          z.object({ type: z.literal('a'), foo: z.string() }),
          z.object({ type: z.literal('b'), foo: z.number() }),
        )

        const [first, second] = getPropertySchema(Schema, ['foo'])

        expect(first).toBeInstanceOf(ZodString)
        expect(second).toBeInstanceOf(ZodNumber)
      })

      it('returns empty if the key is not in the selected branch in a FormBranch', () => {
        const Schema = branch(
          ['type'],
          z.object({ type: z.literal('a'), foo: z.string() }),
          z.object({ type: z.literal('b') }),
        )

        const result = getPropertySchema(Schema, ['foo'], {
          type: 'b',
        })

        expect(result).toEqual([])
      })
    })

    describe('when path has multiple keys', () => {
      it('returns a single sub schema if the schema describes an object', () => {
        const Schema = z.object({
          foo: z.object({ bar: z.string() }),
        })

        expect(getPropertySchema(Schema, ['foo', 'bar'])).toEqual([
          Schema.shape.foo.shape.bar,
        ])
      })

      it('returns a single sub schema if the schema describes an array', () => {
        const Schema = z.object({ foo: z.string() }).array()

        expect(getPropertySchema(Schema, ['0', 'foo'])).toEqual([
          Schema.element.shape.foo,
        ])
      })

      it('returns empty array if the key is not present in the object schema', () => {
        const Schema = z.object({
          foo: z.object({ bar: z.string() }),
        })

        expect(getPropertySchema(Schema, ['foo', 'baz'])).toEqual([])
      })

      it('returns an array of sub schemas if the schema describes a union of objects', () => {
        const Schema = z.union([
          z.object({ foo: z.object({ bar: z.string() }) }),
          z.object({ foo: z.object({ bar: z.number() }) }),
        ])

        const [first, second] = Schema.options.map((s) => s.shape.foo.shape.bar)

        expect(getPropertySchema(Schema, ['foo', 'bar'])).toEqual([
          first,
          second,
        ])
      })

      it('returns a single sub schema if the key is present in only one of the object schemas', () => {
        const Schema = z.union([
          z.object({ foo: z.object({ bar: z.string() }) }),
          z.object({ foo: z.object({ baz: z.number() }) }),
        ])

        const [first, second] = Schema.options

        expect(getPropertySchema(Schema, ['foo', 'bar'])).toEqual([
          first.shape.foo.shape.bar,
        ])

        expect(getPropertySchema(Schema, ['foo', 'baz'])).toEqual([
          second.shape.foo.shape.baz,
        ])
      })

      it('returns empty array if the key is not present in any of the object schemas', () => {
        const Schema = z.union([
          z.object({ foo: z.object({ bar: z.string() }) }),
          z.object({ foo: z.object({ baz: z.number() }) }),
        ])

        expect(getPropertySchema(Schema, ['foo', 'qux'])).toEqual([])
      })

      it('returns empty array if the first key is not present in any of the object schemas', () => {
        const Schema = z.union([
          z.object({ foo: z.object({ bar: z.string() }) }),
          z.object({ foo: z.object({ baz: z.number() }) }),
        ])

        expect(getPropertySchema(Schema, ['qux', 'bar'])).toEqual([])
      })

      it('returns a joint FormOptions schema if the key is a discriminator path', () => {
        const Schema = branch(
          ['foo', 'type'],
          z.object({
            foo: z.object({ type: z.literal('a') }),
            bar: z.string(),
          }),
          z.object({
            foo: z.object({ type: z.literal('b') }),
            bar: z.number(),
          }),
        )

        const [Result, ...rest] = getPropertySchema(Schema, [
          'foo',
          'type',
        ]) as [FormOptions]

        expect(rest).toEqual([])
        expect(Result).toBeInstanceOf(FormOptions)
        expect(Result._def.options).toEqual([
          { label: 'A', value: 'a', metadata: {} },
          { label: 'B', value: 'b', metadata: {} },
        ])
      })

      it('returns multiple sub schemas if the key is not a discriminator path', () => {
        const Schema = branch(
          ['foo', 'type'],
          z.object({
            foo: z.object({ type: z.literal('a'), baz: z.string() }),
            bar: z.string(),
          }),
          z.object({
            foo: z.object({ type: z.literal('b'), baz: z.number() }),
            bar: z.number(),
          }),
        )

        const [first, second] = getPropertySchema(Schema, ['foo', 'baz'])

        expect(first).toBeInstanceOf(ZodString)
        expect(second).toBeInstanceOf(ZodNumber)
      })
    })
  })

  describe('getPropertySchema with input', () => {
    describe('when the schema is a ZodObject', () => {
      test('if the path is a single key and the input is valid, it returns a list of a single sub schema', () => {
        const Schema = z.object({ foo: z.string() })

        const [Result, ...rest] = getPropertySchema(Schema, ['foo'], {
          foo: 'string',
        })

        expect(rest).toEqual([])
        expect(Result).toBeInstanceOf(ZodString)
      })

      test('if the path is a single key and the input is not valid, it returns a list of a single sub schema', () => {
        const Schema = z.object({ foo: z.string() })

        const [Result, ...rest] = getPropertySchema(Schema, ['foo'], {
          foo: 123131,
        })

        expect(rest).toEqual([])
        expect(Result).toBeInstanceOf(ZodString)
      })

      test('if the path has multiple keys, it traverses the schema and returns a full sub schema list', () => {
        const Schema = z.object({
          foo: z.union([
            z.object({ bar: z.string() }),
            z.object({ bar: z.number() }),
          ]),
        })

        const [First, Second, ...rest] = getPropertySchema(
          Schema,
          ['foo', 'bar'],
          { foo: { bar: 'string' } },
        )

        expect(rest).toEqual([])
        expect(First).toBeInstanceOf(ZodString)
        expect(Second).toBeInstanceOf(ZodNumber)
      })
    })

    describe('when the schema is a ZodUnion of ZodObject', () => {
      test('if the path is a single key and the input is valid, it returns a full sub schema list', () => {
        const Schema = z.union([
          z.object({ foo: z.string(), bar: z.null() }),
          z.object({ foo: z.number(), bar: z.null() }),
        ])

        const [First, Second, ...rest] = getPropertySchema(Schema, ['foo'], {
          foo: 'string',
        })

        expect(rest).toEqual([])
        expect(First).toBeInstanceOf(ZodString)
        expect(Second).toBeInstanceOf(ZodNumber)
      })

      test('if the path is a single key and the input is not valid, it returns a full sub schema list', () => {
        const Schema = z.union([
          z.object({ foo: z.string(), bar: z.null() }),
          z.object({ foo: z.number(), bar: z.null() }),
        ])

        const [First, Second, ...rest] = getPropertySchema(Schema, ['foo'], {
          foo: null,
        })

        expect(rest).toEqual([])
        expect(First).toBeInstanceOf(ZodString)
        expect(Second).toBeInstanceOf(ZodNumber)
      })

      test('if the path has multiple keys, it traverses the schema and returns a full sub schema list', () => {
        const Schema = z.union([
          z.object({ foo: z.object({ bar: z.string() }) }),
          z.object({ foo: z.object({ bar: z.number() }) }),
        ])

        const [First, Second, ...rest] = getPropertySchema(
          Schema,
          ['foo', 'bar'],
          { foo: { bar: 'string' } },
        )

        expect(rest).toEqual([])
        expect(First).toBeInstanceOf(ZodString)
        expect(Second).toBeInstanceOf(ZodNumber)
      })
    })

    describe('when the schema is a FormBranch', () => {
      test('if the path is a single key and the input is valid, it returns a narrowed sub schema list', () => {
        const Schema = branch(
          ['type'],
          z.object({ type: z.literal('a'), foo: z.string() }),
          z.object({ type: z.literal('b'), foo: z.number() }),
        )

        // Assert on the 'a' branch

        const [ResultA, ...restA] = getPropertySchema(Schema, ['foo'], {
          type: 'a',
        })

        expect(restA).toEqual([])
        expect(ResultA).toBeInstanceOf(ZodString)

        // Assert on the 'b' branch

        const [ResultB, ...restB] = getPropertySchema(Schema, ['foo'], {
          type: 'b',
        })

        expect(restB).toEqual([])
        expect(ResultB).toBeInstanceOf(ZodNumber)
      })

      test('if the path is a single key and the input is not valid, it returns an empty list', () => {
        const Schema = branch(
          ['type'],
          z.object({ type: z.literal('a'), foo: z.string() }),
          z.object({ type: z.literal('b'), foo: z.number() }),
        )

        const [First, ...rest] = getPropertySchema(Schema, ['foo'], {
          type: 'c',
        })

        expect(rest).toEqual([])
        expect(First).toBeInstanceOf(ZodString)
      })

      test('if the path has multiple keys and the input is valid, it traverses the schema and returns a narrowed sub schema list', () => {
        const Schema = branch(
          ['type'],
          z.object({
            type: z.literal('a'),
            foo: z.object({ bar: z.string() }),
          }),
          z.object({
            type: z.literal('b'),
            foo: z.object({ bar: z.number() }),
          }),
        )

        // Assert on the 'a' branch

        const [FirstA, ...restA] = getPropertySchema(Schema, ['foo', 'bar'], {
          type: 'a',
        })

        expect(restA).toEqual([])
        expect(FirstA).toBeInstanceOf(ZodString)

        // Assert on the 'b' branch

        const [FirstB, ...restB] = getPropertySchema(Schema, ['foo', 'bar'], {
          type: 'b',
        })

        expect(restB).toEqual([])
        expect(FirstB).toBeInstanceOf(ZodNumber)
      })

      test('if the path has multiple keys and the input is not valid, it traverses the schema and return the first option', () => {
        const Schema = branch(
          ['type'],
          z.object({
            type: z.literal('a'),
            foo: z.object({ bar: z.string() }),
          }),
          z.object({
            type: z.literal('b'),
            foo: z.object({ bar: z.number() }),
          }),
        )

        const [First, ...rest] = getPropertySchema(Schema, ['foo', 'bar'], {
          type: 'c',
        })

        expect(rest).toEqual([])
        expect(First).toBeInstanceOf(ZodString)
      })
    })
  })

  describe('getPossibleKeys', () => {
    it('returns an array of keys if the schema describes an object', () => {
      const Schema = z.object({ foo: z.string(), bar: z.number() })

      expect(getPossibleKeys(Schema)).toEqual(['foo', 'bar'])
    })

    it('returns an array of keys if the schema describes an array', () => {
      const Schema = z.object({ foo: z.string(), bar: z.number() }).array()

      expect(getPossibleKeys(Schema)).toEqual(['foo', 'bar'])
    })

    it('returns an array of keys if the schema describes a union of objects', () => {
      const Schema = z.union([
        z.object({ foo: z.string(), bar: z.number() }),
        z.object({ baz: z.boolean() }),
      ])

      expect(getPossibleKeys(Schema)).toEqual(['foo', 'bar', 'baz'])
    })

    it('returns an array of key if the schema is an object intersection', () => {
      const Schema = z
        .object({ foo: z.string() })
        .merge(z.object({ bar: z.number() }))

      expect(getPossibleKeys(Schema)).toEqual(['foo', 'bar'])
    })

    it('does not return duplicate keys', () => {
      const Schema = z.union([
        z.object({ foo: z.string(), bar: z.number() }),
        z.object({ foo: z.string(), baz: z.boolean() }),
      ])

      expect(getPossibleKeys(Schema)).toEqual(['foo', 'bar', 'baz'])
    })

    it('returns an empty array if the schema does not describe an object', () => {
      const Schema = z.union([z.string(), z.number()])

      expect(getPossibleKeys(Schema)).toEqual([])
    })
  })

  describe('getPropertyPossibleKeys', () => {
    describe('when the path is empty', () => {
      it('returns an array of keys if the schema describes an object', () => {
        const Schema = z.object({
          foo: z.string(),
          bar: z.number(),
        })

        expect(getPropertyPossibleKeys(Schema, [])).toEqual(['foo', 'bar'])
      })

      it('returns an array of keys if the schema describes a union of objects', () => {
        const Schema = z.union([
          z.object({ foo: z.string(), bar: z.number() }),
          z.object({ baz: z.boolean() }),
        ])

        expect(getPropertyPossibleKeys(Schema, [])).toEqual([
          'foo',
          'bar',
          'baz',
        ])
      })

      it('does not return duplicate keys', () => {
        const Schema = z.union([
          z.object({ foo: z.string(), bar: z.number() }),
          z.object({ foo: z.string(), baz: z.boolean() }),
        ])

        expect(getPropertyPossibleKeys(Schema, [])).toEqual([
          'foo',
          'bar',
          'baz',
        ])
      })

      it('returns an empty array if the schema does not describe an object', () => {
        const Schema = z.union([z.string(), z.number()])

        expect(getPropertyPossibleKeys(Schema, [])).toEqual([])
      })
    })

    describe('when the path is a single key', () => {
      it('returns an array of keys if the schema describes an object', () => {
        const Schema = z.object({
          sub: z.object({ foo: z.string(), bar: z.number() }),
        })

        expect(getPropertyPossibleKeys(Schema, ['sub'])).toEqual(['foo', 'bar'])
      })

      it('returns an array of keys if the schema describes a union of objects', () => {
        const Schema = z.union([
          z.object({ sub: z.object({ foo: z.string(), bar: z.number() }) }),
          z.object({ sub: z.object({ baz: z.boolean() }) }),
        ])

        expect(getPropertyPossibleKeys(Schema, ['sub'])).toEqual([
          'foo',
          'bar',
          'baz',
        ])
      })

      it('returns empty array if the key is not present in any of the object schemas', () => {
        const Schema = z.union([
          z.object({ sub: z.object({ foo: z.string(), bar: z.number() }) }),
          z.object({ sub: z.object({ baz: z.boolean() }) }),
        ])

        expect(getPropertyPossibleKeys(Schema, ['baz'])).toEqual([])
      })

      it('does not return duplicate keys', () => {
        const Schema = z.union([
          z.object({ sub: z.object({ foo: z.string(), bar: z.number() }) }),
          z.object({ sub: z.object({ foo: z.string(), baz: z.boolean() }) }),
        ])

        expect(getPropertyPossibleKeys(Schema, ['sub'])).toEqual([
          'foo',
          'bar',
          'baz',
        ])
      })

      it('returns an empty array if the schema does not describe an object', () => {
        const Schema = z.union([z.string(), z.number()])

        expect(getPropertyPossibleKeys(Schema, ['sub'])).toEqual([])
      })
    })

    describe('when the path has multiple keys', () => {
      it('returns an array of keys if the schema describes an object', () => {
        const Schema = z.object({
          top: z.object({
            sub: z.object({ foo: z.string(), bar: z.number() }),
          }),
        })

        expect(getPropertyPossibleKeys(Schema, ['top', 'sub'])).toEqual([
          'foo',
          'bar',
        ])
      })

      it('returns an array of keys if the schema describes a union of objects', () => {
        const Schema = z.union([
          z.object({
            top: z.object({
              sub: z.object({
                foo: z.string(),
                bar: z.number(),
              }),
            }),
          }),
          z.object({
            top: z.object({
              sub: z.object({
                baz: z.boolean(),
              }),
            }),
          }),
        ])

        expect(getPropertyPossibleKeys(Schema, ['top', 'sub'])).toEqual([
          'foo',
          'bar',
          'baz',
        ])
      })

      it('returns empty array if the key is not present in any of the object schemas', () => {
        const Schema = z.union([
          z.object({
            top: z.object({
              sub: z.object({
                foo: z.string(),
                bar: z.number(),
              }),
            }),
          }),
          z.object({
            top: z.object({
              sub: z.object({
                baz: z.boolean(),
              }),
            }),
          }),
        ])

        expect(getPropertyPossibleKeys(Schema, ['top', 'baz'])).toEqual([])
      })

      it('does not return duplicate keys', () => {
        const Schema = z.union([
          z.object({
            top: z.object({
              sub: z.object({ foo: z.string(), bar: z.number() }),
            }),
          }),
          z.object({
            top: z.object({
              sub: z.object({ foo: z.string(), baz: z.boolean() }),
            }),
          }),
        ])

        expect(getPropertyPossibleKeys(Schema, ['top', 'sub'])).toEqual([
          'foo',
          'bar',
          'baz',
        ])
      })

      it('returns an empty array if the schema does not describe an object', () => {
        const Schema = z.union([z.string(), z.number()])

        expect(getPropertyPossibleKeys(Schema, ['sub'])).toEqual([])
      })
    })
  })

  describe('getConstraints', () => {
    describe.each([
      { create: z.number, name: 'ZodNumber' },
      { create: number, name: 'FormNumber' },
    ])('when the schema is a $name', ({ create }) => {
      it('returns an object with constraints for an optional and nullable number schema', () => {
        const Schema = z
          .number()
          .min(1)
          .max(10)
          .multipleOf(2)
          .optional()
          .nullable()
        expect(getConstraints(Schema)).toEqual({ min: 1, max: 10, step: 2 })
      })

      it('returns an object with constraints', () => {
        const Schema = create().min(1).max(10).multipleOf(2)

        expect(getConstraints(Schema)).toEqual({ min: 1, max: 10, step: 2 })
      })

      it('returns an object with constraints for a nullable number schema', () => {
        const Schema = create().min(1).max(10).multipleOf(2).nullable()

        expect(getConstraints(Schema)).toEqual({ min: 1, max: 10, step: 2 })
      })

      it('returns an object with constraints for an optional number schema', () => {
        const Schema = create().min(1).max(10).multipleOf(2).optional()

        expect(getConstraints(Schema)).toEqual({ min: 1, max: 10, step: 2 })
      })

      it('returns an object with constraints with a default', () => {
        const Schema = create().min(1).max(10).multipleOf(2).default(5)

        expect(getConstraints(Schema)).toEqual({ min: 1, max: 10, step: 2 })
      })

      it('returns an empty object for a unconstrained number schema', () => {
        const Schema = create()

        expect(getConstraints(Schema)).toEqual({})
      })

      it('returns an empty object for an unconstrainable schema', () => {
        const Schema = z.object({})

        expect(getConstraints(Schema)).toEqual({})
      })

      it('returns an object with options for a FormOptions schema', () => {
        const Schema = options('foo', 'bar', 'baz')

        expect(getConstraints(Schema)).toEqual({
          options: ['foo', 'bar', 'baz'].map((o) => ({
            label: o,
            value: o,
            metadata: {},
          })),
          multi: false,
        })
      })

      it('returns an object with options for a multi-select FormOptions schema', () => {
        const Schema = options('foo', 'bar', 'baz').multi()

        expect(getConstraints(Schema)).toEqual({
          options: ['foo', 'bar', 'baz'].map((o) => ({
            label: o,
            value: o,
            metadata: {},
          })),
          multi: true,
        })
      })

      it('returns an object with options for a resolved async FormOptions schema', async () => {
        function fetch() {
          return Promise.resolve([
            { value: 'foo', label: 'Foo', metadata: {} },
            { value: 'bar', label: 'Bar', metadata: {} },
            { value: 'baz', label: 'Baz', metadata: {} },
          ])
        }

        const Schema = options().async(fetch)

        await Schema.resolve({})

        expect(getConstraints(Schema)).toEqual({
          options: await fetch(),
          multi: false,
        })
      })
    })
  })

  describe('resolveSchema', () => {
    function fetch(_query: Record<string, unknown>) {
      return Promise.resolve([
        { value: 'foo', label: 'Foo', metadata: {} },
        { value: 'bar', label: 'Bar', metadata: {} },
        { value: 'baz', label: 'Baz', metadata: {} },
      ])
    }

    it('resolves a options schema', async () => {
      const Schema = options().async(fetch)

      await resolveSchema(Schema)

      expect(Schema._def.options).toEqual(await fetch({}))
    })

    it('resolves object schema dependencies recursively', async () => {
      const Schema = z.object({
        foo: z.object({ bar: options().async(fetch) }),
        baz: z.string(),
      })

      await resolveSchema(Schema)

      expect(Schema.shape.foo.shape.bar._def.options).toEqual(await fetch({}))
    })

    it('resolves union schema dependencies recursively', async () => {
      const Schema = z.union([
        z.object({ foo: options().async(fetch) }),
        z.object({ bar: options().async(fetch) }),
      ])

      await resolveSchema(Schema)

      const [f, s] = Schema.options

      expect(f.shape.foo._def.options).toEqual(await fetch({}))
      expect(s.shape.bar._def.options).toEqual(await fetch({}))
    })

    it('resolves branch schema dependencies recursively', async () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: options().async(fetch) }),
        z.object({ type: z.literal('b'), bar: options().async(fetch) }),
      )

      await resolveSchema(Schema)

      const [f, s] = Schema.options().map((s) => s.shape) as unknown as [
        { type: ZodLiteral<'a'>; foo: FormOptions },
        { type: ZodLiteral<'b'>; bar: FormOptions },
      ]

      expect(f.foo._def.options).toEqual(await fetch({}))
      expect(s.bar._def.options).toEqual(await fetch({}))
    })

    it('resolves array schema with dependencies', async () => {
      const Schema = z
        .object({
          foo: z.object({ bar: options().async(fetch) }),
          baz: z.string(),
        })
        .array()

      await resolveSchema(Schema)

      expect(Schema.element.shape.foo.shape.bar._def.options).toEqual(
        await fetch({}),
      )
    })
  })

  describe.each([
    [(S: ZodSchema) => S, 'no transform'],
    [(S: ZodSchema) => S.default({}), 'default'],
    [(S: ZodSchema) => S.nullable(), 'nullable'],
    [(S: ZodSchema) => S.optional(), 'optional'],
  ])('parseValid on a ZodObject with %s', (transform) => {
    it('parses a valid object', () => {
      const Schema = transform(
        z.object({
          foo: z.string(),
          bar: z.number(),
          baz: z.boolean().optional(),
          qux: z.string().nullable(),
        }),
      )

      let result = parseValid(Schema, {
        foo: 'foo',
        bar: 1,
        baz: true,
        qux: 'qux',
      })

      expect(result).toEqual({ foo: 'foo', bar: 1, baz: true, qux: 'qux' })

      result = parseValid(Schema, {
        foo: 'foo',
        bar: 1,
        qux: null,
      })

      expect(result).toStrictEqual({ foo: 'foo', bar: 1, qux: null })
    })

    it('parses a valid object keeping only the known properties', () => {
      const Schema = transform(
        z.object({
          foo: z.string(),
          bar: z.number(),
          baz: z.boolean().optional(),
        }),
      )

      const result = parseValid(Schema, {
        foo: 'foo',
        bar: 1,
        baz: true,
        qux: 'qux',
      })

      expect(result).toStrictEqual({ foo: 'foo', bar: 1, baz: true })
    })

    it('parses a partially valid object keeping only the valid properties', () => {
      const Schema = transform(
        z.object({
          foo: z.string(),
          bar: z.number(),
          baz: z.boolean().optional(),
        }),
      )

      const result = parseValid(Schema, {
        foo: 'foo',
        bar: 'bar',
        qux: 'qux',
      })

      expect(result).toStrictEqual({ foo: 'foo' })
    })

    it('parses a partially valid object recursively', () => {
      const Schema = transform(
        z.object({
          foo: z.object({ bar: z.string(), qux: z.number() }),
          baz: z.string(),
          qux: z.number(),
        }),
      )

      const result = parseValid(Schema, {
        foo: { bar: 'bar', qux: null },
        baz: 'baz',
        qux: null,
      })

      expect(result).toEqual({
        foo: { bar: 'bar' },
        baz: 'baz',
      })
    })

    it('parses a partially valid object with sub-objects recursively', () => {
      const Schema = transform(
        z.object({
          foo: transform(z.object({ baz: z.string() })),
          bar: z.object({ qux: z.string(), baz: z.number() }),
        }),
      )

      const result = parseValid(Schema, {
        foo: { baz: 'baz', qux: 'qux' },
        bar: { qux: 'qux', baz: 'baz' },
      })

      expect(result).toEqual({
        foo: { baz: 'baz' },
        bar: { qux: 'qux' },
      })
    })

    it('parses an invalid object', () => {
      const Schema = transform(
        z.object({
          foo: z.string(),
          bar: z.number(),
        }),
      )

      const result = parseValid(Schema, {
        foo: 2,
        bar: 'bar',
      })

      expect(result).toStrictEqual({})
    })
  })

  describe('parseValid on a FormBranch', () => {
    it('parses a valid branch', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      let result = parseValid(Schema, {
        type: 'a',
        foo: 'foo',
      })

      expect(result).toStrictEqual({ type: 'a', foo: 'foo' })

      result = parseValid(Schema, {
        type: 'b',
        bar: 1,
      })

      expect(result).toStrictEqual({ type: 'b', bar: 1 })

      result = parseValid(Schema, {
        type: 'b',
      })

      expect(result).toStrictEqual({ type: 'b' })
    })

    it('parses a valid branch keeping only the known properties', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      let result = parseValid(Schema, {
        type: 'a',
        foo: 'foo',
        bar: 1,
      })

      expect(result).toStrictEqual({ type: 'a', foo: 'foo' })

      result = parseValid(Schema, {
        type: 'b',
        foo: 'foo',
        bar: 1,
      })

      expect(result).toStrictEqual({ type: 'b', bar: 1 })
    })

    it('parses a partially valid branch keeping only the valid properties', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string(), qux: z.number() }),
        z.object({ type: z.literal('b'), bar: z.number(), qux: z.number() }),
      )

      let result = parseValid(Schema, {
        type: 'a',
        foo: 'foo',
        bar: 1,
        qux: 'qux',
      })

      expect(result).toStrictEqual({ type: 'a', foo: 'foo' })

      result = parseValid(Schema, {
        type: 'b',
        foo: 'foo',
        bar: 1,
        qux: 'qux',
      })

      expect(result).toStrictEqual({ type: 'b', bar: 1 })
    })

    it('parses a partially valid branch recursively', () => {
      const Schema = branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.string(),
          qux: branch(
            ['type'],
            z.object({
              type: z.literal('a'),
              foo: z.string(),
              bar: z.number(),
            }),
            z.object({
              type: z.literal('b'),
              foo: z.string(),
              bar: z.number(),
            }),
          ),
        }),
        z.object({
          type: z.literal('b'),
          bar: z.number(),
          qux: z.number(),
        }),
      )

      let result = parseValid(Schema, {
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'a',
          foo: 'foo',
          bar: 'bar',
          qux: null,
        },
      })

      expect(result).toStrictEqual({
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'a',
          foo: 'foo',
        },
      })

      result = parseValid(Schema, {
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'b',
          foo: 1,
          bar: 1,
          qux: null,
        },
      })

      expect(result).toStrictEqual({
        type: 'a',
        foo: 'foo',
        qux: {
          type: 'b',
          bar: 1,
        },
      })
    })

    it('parses a invalid branch', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      const result = parseValid(Schema, {
        type: 'c',
        foo: 'foo',
        bar: 'bar',
      })

      expect(result).toStrictEqual({
        foo: 'foo',
      })
    })
  })

  describe('parseValid on a ZodUnion of ZodObject', () => {
    it('parses a unambiguous valid union', () => {
      const Schema = z.union([
        z.object({ foo: z.string() }),
        z.object({ bar: z.number() }),
      ])

      const result = parseValid(Schema, {
        foo: 'foo',
      })

      expect(result).toStrictEqual({ foo: 'foo' })
    })

    it('parses a unambiguous valid union keeping only the known properties', () => {
      const Schema = z.union([
        z.object({ foo: z.string() }),
        z.object({ bar: z.number() }),
      ])

      const result = parseValid(Schema, {
        foo: 'foo',
        bar: 1,
        baz: 'baz',
      })

      expect(result).toStrictEqual({ foo: 'foo' })
    })

    it('parses a ambiguous valid union keeping all known properties of the first option', () => {
      const Schema = z.union([
        z.object({ foo: z.string() }),
        z.object({ bar: z.number() }),
      ])

      const result = parseValid(Schema, {
        foo: 'foo',
        bar: 1,
        baz: 'baz',
      })

      expect(result).toStrictEqual({ foo: 'foo' })
    })

    it('parses a unambiguous partially valid union keeping only the valid properties', () => {
      const Schema = z.union([
        z.object({ foo: z.string(), baz: z.number() }),
        z.object({ bar: z.number(), baz: z.number() }),
      ])

      const result = parseValid(Schema, {
        foo: 'foo',
        baz: 'baz',
      })

      expect(result).toStrictEqual({ foo: 'foo' })
    })

    it('parses a ambiguous partially valid union keeping all known properties', () => {
      const Schema = z.union([
        z.object({ foo: z.string(), baz: z.number() }),
        z.object({ bar: z.number(), baz: z.number() }),
      ])

      const result = parseValid(Schema, {
        foo: 'foo',
        bar: 1,
        baz: 'baz',
      })

      expect(result).toStrictEqual({ foo: 'foo', bar: 1 })
    })

    it('parses a completely invalid union, returning empty object', () => {
      const Schema = z.union([
        z.object({ foo: z.string() }),
        z.object({ bar: z.number() }),
      ])

      const result = parseValid(Schema, {
        foo: 1,
        bar: 'bar',
        baz: 'baz',
      })

      expect(result).toStrictEqual({})
    })
  })

  describe('parseValid on a ZodArray', () => {
    it('parses a valid array', () => {
      const Schema = z.array(
        z.object({
          foo: z.string(),
          bar: z.number(),
          baz: z.boolean().optional(),
          qux: z.string().nullable(),
        }),
      )

      let result = parseValid(Schema, [
        {
          foo: 'foo',
          bar: 1,
          baz: true,
          qux: 'qux',
        },
      ])

      expect(result).toEqual([{ foo: 'foo', bar: 1, baz: true, qux: 'qux' }])

      result = parseValid(Schema, [
        {
          foo: 'foo',
          bar: 1,
          qux: null,
        },
      ])

      expect(result).toStrictEqual([{ foo: 'foo', bar: 1, qux: null }])
    })

    it('parses a valid array with multiple objects', () => {
      const Schema = z.array(
        z.object({
          foo: z.string(),
          bar: z.number(),
          baz: z.boolean().optional(),
          qux: z.string().nullable(),
        }),
      )

      let result = parseValid(Schema, [
        {
          foo: 'foo',
          bar: 1,
          baz: true,
          qux: 'qux',
        },
        {
          foo: 'foo',
          bar: 2,
          baz: false,
          qux: 'qux',
        },
      ])

      expect(result).toEqual([
        { foo: 'foo', bar: 1, baz: true, qux: 'qux' },
        { foo: 'foo', bar: 2, baz: false, qux: 'qux' },
      ])

      result = parseValid(Schema, [
        {
          foo: 'foo',
          bar: 1,
          qux: null,
        },
        {
          foo: 'foo',
          bar: 2,
          baz: false,
          qux: 'qux',
        },
      ])

      expect(result).toStrictEqual([
        { foo: 'foo', bar: 1, qux: null },
        { foo: 'foo', bar: 2, baz: false, qux: 'qux' },
      ])
    })

    it('parses a valid array keeping only the known properties', () => {
      const Schema = z.array(
        z.object({
          foo: z.string(),
          bar: z.number(),
          baz: z.boolean().optional(),
        }),
      )

      const result = parseValid(Schema, [
        {
          foo: 'foo',
          bar: 1,
          baz: true,
          qux: 'qux',
        },
      ])

      expect(result).toStrictEqual([{ foo: 'foo', bar: 1, baz: true }])
    })

    it('parses a partially valid array keeping only the valid properties', () => {
      const Schema = z.array(
        z.object({
          foo: z.string(),
          bar: z.number(),
          baz: z.boolean().optional(),
        }),
      )

      const result = parseValid(Schema, [
        {
          foo: 'foo',
          bar: 'bar',
          qux: 'qux',
        },
      ])

      expect(result).toStrictEqual([{ foo: 'foo' }])
    })

    it('parses a partially valid array recursively', () => {
      const Schema = z.array(
        z.object({
          foo: z.object({ bar: z.string(), qux: z.number() }),
          baz: z.string(),
          qux: z.number(),
        }),
      )

      const result = parseValid(Schema, [
        {
          foo: { bar: 'bar', qux: null },
          baz: 'baz',
          qux: null,
        },
      ])

      expect(result).toEqual([
        {
          foo: { bar: 'bar' },
          baz: 'baz',
        },
      ])
    })

    it('parses a partially valid array with sub-objects recursively', () => {
      const Schema = z.array(
        z.object({
          foo: z.object({ baz: z.string() }),
          bar: z.object({ qux: z.string(), baz: z.number() }),
        }),
      )

      const result = parseValid(Schema, [
        {
          foo: { baz: 'baz', qux: 'qux' },
          bar: { qux: 'qux', baz: 'baz' },
        },
      ])

      expect(result).toEqual([
        {
          foo: { baz: 'baz' },
          bar: { qux: 'qux' },
        },
      ])
    })

    it('parses an invalid array', () => {
      const Schema = z.array(
        z.object({
          foo: z.string(),
          bar: z.number(),
        }),
      )

      const result = parseValid(Schema, [
        {
          foo: 2,
          bar: 'bar',
        },
      ])

      expect(result).toStrictEqual([{}])
    })
  })

  describe('isPropertyValid', () => {
    it('returns true if the property of a ZodObject is valid', () => {
      const Schema = z.object({ foo: z.string() })

      expect(isPropertyValid(Schema, ['foo'], { foo: 'foo' })).toBe(true)
    })

    it('returns false if the property of a ZodObject is invalid', () => {
      const Schema = z.object({ foo: z.string() })

      expect(isPropertyValid(Schema, ['foo'], { foo: 123 })).toBe(false)
    })

    it('returns true if the property is valid for every ZodObject in a ZodUnion', () => {
      const Schema = z.union([
        z.object({ foo: z.string() }),
        z.object({ foo: z.string().datetime(), bar: z.number() }),
      ])

      const result = isPropertyValid(Schema, ['foo'], {
        foo: '1997-06-08T03:00:00Z',
      })

      expect(result).toBe(true)
    })

    it('returns false if the property is invalid for at least one ZodObject in a ZodUnion', () => {
      const Schema = z.union([
        z.object({ foo: z.string() }),
        z.object({ foo: z.string().datetime(), bar: z.number() }),
      ])

      const result = isPropertyValid(Schema, ['foo'], {
        foo: 'foo',
      })

      expect(result).toBe(false)
    })

    it('returns true if the property is valid for the selected branch of a FormBranch', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), bar: z.number() }),
      )

      const result = isPropertyValid(Schema, ['foo'], {
        type: 'a',
        foo: 'foo',
      })

      expect(result).toBe(true)
    })

    it('returns false if the property is invalid for the selected branch of a FormBranch', () => {
      const Schema = branch(
        ['type'],
        z.object({ type: z.literal('a'), foo: z.string() }),
        z.object({ type: z.literal('b'), foo: z.number(), bar: z.number() }),
      )

      const result = isPropertyValid(Schema, ['foo'], {
        type: 'b',
        foo: 'foo',
      })

      expect(result).toBe(false)
    })
  })

  describe('getPropertyErrors', () => {
    it('returns an empty object if the property of a ZodObject is valid', () => {
      const Schema = z.object({
        foo: z.object({
          bar: z.string(),
          baz: z.number(),
          boo: z.boolean(),
        }),
      })

      const result = getPropertyErrors(Schema, ['foo'], {
        foo: {
          bar: 'bar',
          baz: 1,
          boo: true,
        },
      })

      expect(result).toStrictEqual({})
    })

    it('returns errors if the property of a ZodObject is invalid', () => {
      const Schema = z.object({
        foo: z.object({
          bar: z.string(),
          baz: z.number(),
          boo: z.boolean(),
        }),
      })

      const result = getPropertyErrors(Schema, ['foo'], {
        foo: {
          bar: 123,
          baz: 'baz',
        },
      })

      expect(result).toStrictEqual({
        bar: 'Expected string, received number',
        baz: 'Expected number, received string',
        boo: 'Required',
      })
    })

    it('returns an empty object if the property is valid for every ZodObject in a ZodUnion', () => {
      const Schema = z.union([
        z.object({
          foo: z.object({
            bar: z.string(),
            baz: z.number(),
          }),
        }),
        z.object({
          foo: z.object({
            bar: z.string().datetime(),
            baz: z.number(),
          }),
        }),
      ])

      const result = getPropertyErrors(Schema, ['foo'], {
        foo: {
          bar: '1997-06-08T03:00:00Z',
          baz: 123,
        },
      })

      expect(result).toStrictEqual({})
    })

    it('returns errors if the property is invalid for every ZodObject in a ZodUnion', () => {
      const Schema = z.union([
        z.object({
          foo: z.object({
            bar: z.string().cuid(),
            baz: z.number(),
          }),
        }),
        z.object({
          foo: z.object({
            bar: z.string().datetime(),
            baz: z.number(),
          }),
        }),
      ])

      const result = getPropertyErrors(Schema, ['foo'], {
        foo: {
          baz: 123,
        },
      })

      expect(result).toStrictEqual({
        bar: 'Required',
      })
    })

    it('returns empty object if the property is valid for the selected branch of a FormBranch', () => {
      const Schema = branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            bar: z.string(),
            baz: z.number(),
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            bar: z.number(),
            baz: z.string(),
          }),
        }),
      )

      const result = getPropertyErrors(Schema, ['foo'], {
        type: 'a',
        foo: {
          bar: 'bar',
          baz: 123,
        },
      })

      expect(result).toStrictEqual({})
    })

    it('returns errors if the property is invalid for the selected branch of a FormBranch', () => {
      const Schema = branch(
        ['type'],
        z.object({
          type: z.literal('a'),
          foo: z.object({
            bar: z.string(),
            baz: z.number(),
          }),
        }),
        z.object({
          type: z.literal('b'),
          foo: z.object({
            bar: z.number(),
            baz: z.string(),
          }),
        }),
      )
      const result = getPropertyErrors(Schema, ['foo'], {
        type: 'a',
        foo: {
          bar: 123,
          baz: 'baz',
        },
      })

      expect(result).toStrictEqual({
        bar: 'Expected string, received number',
        baz: 'Expected number, received string',
      })
    })
  })
})

describe('mergeObjects', () => {
  it('merges two objects with the same shape', () => {
    const Result = mergeObjectSchemas(
      z.object({ foo: z.string(), bar: z.number() }),
      z.object({ foo: z.string(), bar: z.number() }),
    )

    expect(Result.shape).toEqual({
      foo: expect.any(ZodString) as unknown,
      bar: expect.any(ZodNumber) as unknown,
    })
  })

  it('merges two objects with different shapes', () => {
    const Result = mergeObjectSchemas(
      z.object({ foo: z.string() }),
      z.object({ bar: z.number() }),
    )

    expect(Result.shape).toEqual({
      foo: expect.any(ZodString) as unknown,
      bar: expect.any(ZodNumber) as unknown,
    })
  })

  it('merges two objects with nested objects', () => {
    const Result = mergeObjectSchemas(
      z.object({
        foo: z.object({
          bar: z.object({
            baz: z.string(),
          }),
        }),
        baz: z.number(),
      }),
      z.object({
        foo: z.object({
          bar: z.object({
            qux: z.number(),
          }),
        }),
        quux: z.boolean(),
      }),
    )

    expect(Result.shape).toEqual({
      foo: expect.any(ZodObject) as unknown,
      baz: expect.any(ZodNumber) as unknown,
      quux: expect.any(ZodBoolean) as unknown,
    })

    expect(Result.shape.foo).toBeInstanceOf(ZodObject)
    const foo = Result.shape.foo as ZodObject<any>
    expect(foo.shape).toEqual({
      bar: expect.any(ZodObject) as unknown,
    })
    const bar = (foo.shape as Record<string, unknown>).bar as ZodObject<any>
    expect(bar.shape).toEqual({
      baz: expect.any(ZodString) as unknown,
      qux: expect.any(ZodNumber) as unknown,
    })
  })
})
