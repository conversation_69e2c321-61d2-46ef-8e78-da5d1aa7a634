import { ReferenceDocument } from '@/features/documents/types/ReferenceDocument'
import { Railcar } from '@/features/railcars/types/Railcar'
import { ServiceEvent } from '@/features/service-events/types/ServiceEvent'
import { loadForm, loadFormContent } from '../api'
import { getFormHTMLExport } from '../providers/FormProvider'
import {
  FormContent,
  FormMRData,
  FormSignatureHistory,
  FormSignatureHistoryEntry,
  FormSignatureHistoryEntryType,
  FormStatusCode,
  getFormResponseSchema,
  getInitialFormContent,
} from '../types/Form'

/**
 * Headlessly generate an HTML export for the specified form, asynchronously
 * fetching the form structure and content via server action. This mechanism
 * allows form actions to generate an export without having to proactively fetch
 * any records beyond the summary data already present in the form list.
 *
 * @param formId - The ID of the form to export.
 * @param railcar - The associated Railcar data payload.
 * @param serviceEvent - The associated ServiceEvent data payload.
 * @returns An HTML string representing the form's current content.
 */
export const exportForm = async (
  formId: string,
  railcar: Railcar,
  serviceEvent: ServiceEvent,
  referenceDocuments: ReferenceDocument[],
  mrData: FormMRData,
) => {
  const formResponse = await loadForm(formId)

  if (!formResponse.success) {
    throw Error('Could not export form due to load error')
  }

  const form = formResponse.response
  const Schema = await getFormResponseSchema(form)

  let content: FormContent
  if (form.statusCode === FormStatusCode.NOT_STARTED) {
    content = getInitialFormContent(form, mrData)
  } else {
    const contentResponse = await loadFormContent(formId)
    if (!contentResponse.success) {
      throw Error('Could not export form due to content load error')
    }

    content = contentResponse.response
  }

  return getFormHTMLExport({
    form,
    railcar,
    serviceEvent,
    content,
    Schema,
    referenceDocuments,
    mrData,
  })
}

/**
 * Adds a signature history entry to the signature history for the provided signable id.
 *
 * @param id the id of the signable component of the form.
 * @param user the user performing the action.
 * @param action the action performed on the signature.
 * @param signatureHistory the current signature history.
 * @returns the updated signature history.
 */

export const addSignatureHistoryEntry = (
  id: string,
  user: { id: string; name: string },
  action: FormSignatureHistoryEntryType,
  signatureHistory: FormSignatureHistory,
) => {
  const existingSignatureHistory = signatureHistory[id] ?? []
  const newSignatureHistoryEntry = createFormSignatureHistoryEntry(user, action)

  const result = {
    ...signatureHistory,
    [id]: [...existingSignatureHistory, newSignatureHistoryEntry],
  }

  return result
}

/**
 * Creates a signature history entry for the provided signable id.
 *
 * @param signableId the id of the signable component of the form.
 * @param signedBy the user performing the action.
 * @param action the action performed on the signature.
 * @returns the signature history entry.
 */

function createFormSignatureHistoryEntry(
  signedBy: { id: string; name: string },
  action: FormSignatureHistoryEntryType,
): FormSignatureHistoryEntry {
  const signedAt = new Date().toISOString()
  const userInfo = {
    id: signedBy.id,
    name: signedBy.name,
  }

  return FormSignatureHistoryEntry.parse({
    signedBy: userInfo,
    signedAt: signedAt,
    action,
  })
}
