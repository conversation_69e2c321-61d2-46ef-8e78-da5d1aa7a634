import PatternWithRows from '@/features/forms/components/patterns/PatternWithRows'
import { useI18n } from '@gatx-corp/platform-one-common'
import DecalRow from './components/DecalRow'

const Decal = () => {
  const { t } = useI18n('Forms', {
    keyPrefix: 'Decal',
  })

  return (
    <div className="flex flex-col gap-lg print:gap-xs @container">
      <div className="text-body-xs my-sm print:my-0">{t('subtitle')}</div>
      <PatternWithRows RowComponent={DecalRow} removable />
    </div>
  )
}

export default Decal
