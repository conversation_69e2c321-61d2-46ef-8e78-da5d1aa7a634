import { useI18n } from '@gatx-corp/platform-one-common'
import clsx from 'clsx'
import FormChoiceGroupField from '../../components/FormChoiceGroupField'
import FormTextField from '../../components/FormTextField'
import { useFormFields } from '../../hooks/useFormFields'
import { NO } from '../../types/FormSchemaConstants'

const IVPField = ({
  id,
  patternName,
  CorrectionElement,
  mrSuffix,
}: {
  id: string
  patternName: string
  CorrectionElement: React.ElementType
  mrSuffix?: string
}) => {
  mrSuffix = mrSuffix ?? '_MR'
  const { t } = useI18n('Forms')
  const fields = useFormFields({ signable: true })
  const mrField = fields[`${id}${mrSuffix}`]
  const replyField = fields[`${id}_reply`]
  const correctionField = fields[id]

  return (
    <div className="contents">
      <div
        className={clsx(
          (fields.allCorrect?.value !== NO || replyField?.hidden) &&
            'col-span-3',
        )}
      >
        <FormTextField
          readOnly
          value={mrField?.value ? (mrField.value as string) : t('none')}
          label={t(`${patternName}.${id}${mrSuffix}`, { defaultValue: '' })}
          onChange={() => undefined}
        />
      </div>
      {fields.allCorrect?.value === NO && replyField && (
        <div
          className={clsx(
            (!replyField?.value || correctionField?.hidden) && 'col-span-2',
          )}
        >
          <FormChoiceGroupField
            size="large"
            {...replyField}
            label={t('common.response')}
          />
        </div>
      )}

      {replyField?.value && correctionField && (
        <CorrectionElement
          {...correctionField}
          label={t('common.correction')}
        />
      )}
    </div>
  )
}

export { IVPField }
