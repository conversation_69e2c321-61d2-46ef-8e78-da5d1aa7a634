import { AlphanumericString } from '@/features/forms/types/Field'
import branch, { merge } from '@/features/forms/types/FormBranch'
import { No, Yes } from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { mergeAll } from '../../../types/FormBranch'
import { mr, reply } from '../../types/MRData'

const requiredTextField = z.string().min(1, { message: 'Required' })

const WithLightWeightReply = reply('lightWeight', requiredTextField)
const WithLightWeightDateReply = reply('lightWeightDate', requiredTextField)
const WithLightWeightByReply = reply('lightWeightBy', AlphanumericString.max(4))

const IVPLightweight = mr({
  withData: merge(
    z.object({
      lightWeight_MR: z.string(),
      lightWeightDate_MR: z.string(),
      lightWeightBy_MR: z.string(),
    }),
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      merge(
        z.object({
          allCorrect: No,
        }),
        mergeAll(
          WithLightWeightByReply,
          WithLightWeightDateReply,
          WithLightWeightReply,
        ),
      ),
    ),
  ),
  withoutData: {
    lightWeight: requiredTextField,
    lightWeightDate: z.string(),
    lightWeightBy: AlphanumericString.max(4),
  },
})
type IVPLightweight = z.infer<typeof IVPLightweight>

export { IVPLightweight }
