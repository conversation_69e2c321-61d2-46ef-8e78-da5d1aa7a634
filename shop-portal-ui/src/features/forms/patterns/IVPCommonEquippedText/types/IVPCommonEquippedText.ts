import { mr } from '@/features/forms/patterns/types/MRData'
import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { z } from 'zod'

const IVPCommonEquippedText = mr({
  withData: merge(
    z.object({
      currentData_MR: z.string().optional(),
    }),
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      merge(
        z.object({
          allCorrect: No,
        }),
        branch(
          ['currentData_reply'],
          z.object({
            currentData_reply: options("Can't Tell"),
          }),
          z.object({
            currentData_reply: options('Change'),
            currentData: z.string().min(1, { message: 'Required' }),
          }),
        ),
      ),
    ),
  ),
  withoutData: {
    currentData: z.string().min(1, { message: 'Required' }),
  },
})

type IVPCommonEquippedText = z.infer<typeof IVPCommonEquippedText>

export { IVPCommonEquippedText }
