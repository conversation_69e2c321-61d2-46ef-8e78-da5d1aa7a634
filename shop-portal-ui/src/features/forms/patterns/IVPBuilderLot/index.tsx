import FormChoiceGroupField from '@/features/forms/components/FormChoiceGroupField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { FALSE, TRUE } from '../../types/FormSchemaConstants'
import { IVPField } from '../components/IVPField'

const IVPBuilderLot = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ signable: true })

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex gap-2xl mt-xl">
            {fields.builderName && (
              <FormTextField
                {...fields.builderName}
                label={t('IVPBuilderLot.builderName_MR')}
              />
            )}
            {fields.lotNumber && (
              <FormTextField
                {...fields.lotNumber}
                label={t('IVPBuilderLot.lotNumber_MR')}
              />
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField size="large" {...fields.allCorrect} />
          )}

          <div className="inline-grid grid-cols-[auto_auto_1fr] gap-md mt-xl items-start">
            <IVPField
              id="builderName"
              patternName="IVPBuilderLot"
              CorrectionElement={FormTextField}
            />
            <IVPField
              id="lotNumber"
              patternName="IVPBuilderLot"
              CorrectionElement={FormTextField}
            />
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPBuilderLot
