import {
  NonBlankString,
  RequiredStringWithMaxLength,
} from '@/features/forms/types/Field'
import branch, { merge, mergeAll } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { mr } from '../../types/MRData'

const WithBuilderNameReply = branch(
  ['builderName_reply'],
  z.object({
    builderName_reply: options('OK'),
  }),
  z.object({
    builderName_reply: options("Can't Tell"),
  }),
  z.object({
    builderName_reply: options('Change'),
    builderName: RequiredStringWithMaxLength(5),
  }),
)

const WithLotNumberReply = branch(
  ['lotNumber_reply'],
  z.object({
    lotNumber_reply: options('OK'),
  }),
  z.object({
    lotNumber_reply: options("Can't Tell"),
  }),
  z.object({
    lotNumber_reply: options('Change'),
    lotNumber: NonBlankString,
  }),
)

const IVPBuilderLot = mr({
  withData: merge(
    z.object({
      builderName_MR: z.string().optional(),
      lotNumber_MR: z.string().optional(),
    }),
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      merge(
        z.object({
          allCorrect: No,
        }),
        mergeAll(WithBuilderNameReply, WithLotNumberReply),
      ),
    ),
  ),
  withoutData: {
    builderName: RequiredStringWithMaxLength(5),
    lotNumber: NonBlankString,
  },
})

type IVPBuilderLot = z.infer<typeof IVPBuilderLot>

export { IVPBuilderLot }
