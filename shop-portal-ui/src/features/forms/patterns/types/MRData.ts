import { z, <PERSON><PERSON><PERSON><PERSON><PERSON>ha<PERSON>, ZodTypeAny } from 'zod'
import branch, { FormBranch, merge } from '../../types/FormBranch'
import options, { False, OK, True } from '../../types/FormOptions'
import { HAS_MR_DATA } from '../../types/FormSchemaConstants'

const WithMRData = z.object({
  [HAS_MR_DATA]: True,
  // FIXME: this may not apply to every pattern using this utility.
  currentData_MR: z.string().optional(),
})

const WithNoMRData = z.object({
  [HAS_MR_DATA]: False,
})

function mr<
  O extends readonly [ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]],
  S extends ZodRawShape,
>({ withData, withoutData }: { withData: FormBranch<O>; withoutData: S }) {
  return branch(
    // @ts-expect-error TODO: enhance type inference.
    [HAS_MR_DATA],
    merge(WithMRData, withData),
    WithNoMRData.extend(withoutData),
  )
}

const reply = (fieldName: string, fieldSchema: z.ZodSchema) =>
  branch(
    [`${fieldName}_reply`],
    z.object({ [`${fieldName}_reply`]: OK }),
    z.object({ [`${fieldName}_reply`]: options("Can't Tell") }),
    z.object({
      [`${fieldName}_reply`]: options('Change'),
      [fieldName]: fieldSchema,
    }),
  )

export { mr, reply, WithMRData, WithNoMRData }
