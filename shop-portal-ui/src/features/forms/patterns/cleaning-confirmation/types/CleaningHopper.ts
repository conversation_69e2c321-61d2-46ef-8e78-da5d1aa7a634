import { z } from 'zod'

import { merge } from '@/features/forms/types/FormBranch'
import { CantTell, No, Yes } from '@/features/forms/types/FormOptions'
import { CleaningConfirmationSectionId } from '../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { WithCleaningInspection } from './sections/CleaningInspection'
import { CommonCleaningQA } from './sections/CleaningQA'

const CleaningHopperResponse = merge(
  z.object({
    cleaningInspection: z.object({
      solidsPresent: Yes.$or(No).$or(CantTell),
    }),
    cleaningQA: CommonCleaningQA,
  }),
  WithCleaningInspection,
)

type CleaningHopperResponse = z.infer<typeof CleaningHopperResponse>

export {
  CleaningConfirmationSectionId as CleaningFormSectionId,
  CleaningHopperResponse,
}
