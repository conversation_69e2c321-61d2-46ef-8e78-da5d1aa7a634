import { Percentage, PositiveFloat } from '@/features/forms/types/Field'
import { merge } from '@/features/forms/types/FormBranch'
import number from '@/features/forms/types/FormNumber'
import { CantTell, No, Yes } from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { CleaningConfirmationSectionId } from '../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { WithCleaningInspection } from './sections/CleaningInspection'
import { CommonCleaningQA } from './sections/CleaningQA'

const CleaningGeneralQA = CommonCleaningQA.extend({
  LEL: Percentage.nullish(),
})

const CleaningGeneralResponse = merge(
  z.object({
    cleaningInspection: z.object({
      solidsPresent: Yes.$or(No).$or(CantTell),
      pH: number().min(0).max(14).step(0.1).nullish(),
    }),
    cleaningQA: CleaningGeneralQA,
    cleaningInformation: z.object({
      totalFlareHours: PositiveFloat.nullish(),
    }),
  }),
  WithCleaningInspection,
)

type CleaningGeneralResponse = z.infer<typeof CleaningGeneralResponse>

export {
  CleaningConfirmationSectionId as CleaningFormSectionId,
  CleaningGeneralResponse,
}
