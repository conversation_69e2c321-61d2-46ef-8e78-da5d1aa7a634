import { mr } from '@/features/forms/patterns/types/MRData'
import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { z } from 'zod'

const IVPOutletGateNotEquipped = mr({
  withData: merge(
    z.object({
      manufacturer_MR: z.string().optional(),
      model_MR: z.string().optional(),
      type_MR: z.string().optional(),
      material_MR: z.string().optional(),
      connectionType_MR: z.string().optional(),
      location_MR: z.string().optional(),
    }),
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      merge(
        z.object({
          allCorrect: No,
        }),
        branch(
          ['manufacturer_reply'],
          z.object({
            manufacturer_reply: options("Can't Tell"),
          }),
          z.object({
            manufacturer_reply: options('Change'),
            manufacturer: z.string().min(1, { message: 'Required' }),
          }),
        ),
      ),
    ),
  ),
  withoutData: {
    manufacturer: z.string().min(1, { message: 'Required' }),
    model: z.string().optional(),
    type: z.string().optional(),
    material: z.string().optional(),
    connectionType: z.string().optional(),
    location: z.string().optional(),
  },
})
type IVPOutletGateNotEquipped = z.infer<typeof IVPOutletGateNotEquipped>

export { IVPOutletGateNotEquipped }
