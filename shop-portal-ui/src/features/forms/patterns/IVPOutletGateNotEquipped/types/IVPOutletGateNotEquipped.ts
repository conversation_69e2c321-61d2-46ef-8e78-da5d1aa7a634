import { z } from 'zod'

const IVPOutletGateNotEquipped = z
  .object({
    manufacturerMr_disp: z.string().optional(),
    modelMr_disp: z.string().optional(),
    typeMr_disp: z.string().optional(),
    materialMr_disp: z.string().optional(),
    connectionTypeMr_disp: z.string().optional(),
    locationMr_disp: z.string().optional(),
  })
  .array()

type IVPOutletGateNotEquipped = z.infer<typeof IVPOutletGateNotEquipped>

export { IVPOutletGateNotEquipped }
