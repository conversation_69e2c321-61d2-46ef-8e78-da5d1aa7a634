import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { IVPField } from '../components/IVPField'

const IVPOutletGateNotEquipped = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ signable: true })

  return (
    <fieldset>
      <>
        <div className="inline-grid grid-cols-[auto_auto_1fr] gap-md mt-xl items-start">
          <IVPField
            id="manufacturer"
            patternName="IVPOutletGateNotEquipped"
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="model"
            patternName="IVPOutletGateNotEquipped"
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="type"
            patternName="IVPOutletGateNotEquipped"
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="material"
            patternName="IVPOutletGateNotEquipped"
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="connectionType"
            patternName="IVPOutletGateNotEquipped"
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="location"
            patternName="IVPOutletGateNotEquipped"
            CorrectionElement={FormTextField}
          />
        </div>
      </>
    </fieldset>
  )
}

export default IVPOutletGateNotEquipped
