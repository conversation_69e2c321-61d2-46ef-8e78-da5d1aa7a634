import FormTextField from '@/features/forms/components/FormTextField'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { useFormFields } from '../../hooks/useFormFields'

const IVPOutletGateNotEquipped = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ signable: true })

  console.log('fields -->', fields)

  const patternName = 'IVPOutletGateNotEquipped'
  return (
    <fieldset>
      <>
        <Alert
          compact
          level="info"
          title={t(`${patternName}.alertTitle`)}
          urgency="immediate"
        >
          {t(`${patternName}.alertDescription`)}
        </Alert>
        <div className="inline-grid grid-cols-[auto_auto_1fr] gap-md mt-md items-start">
          <div className="contents">
            <FormTextField
              readOnly
              value={
                fields['manufacturerMr_disp']?.value
                  ? (fields['manufacturerMr_disp'].value as string)
                  : t('none')
              }
              label={t(`${patternName}.${fields['manufacturerMr_disp']}`, {
                defaultValue: '',
              })}
              onChange={() => undefined}
            />
          </div>
        </div>
      </>
    </fieldset>
  )
}

export default IVPOutletGateNotEquipped
