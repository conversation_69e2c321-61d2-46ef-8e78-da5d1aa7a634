import FormTextField from '@/features/forms/components/FormTextField'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { useFormFields } from '../../hooks/useFormFields'
import { IVPField } from '../components/IVPField'

const IVPOutletGateNotEquipped = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ signable: true })

  const patternName = 'IVPOutletGateNotEquipped'
  return (
    <fieldset>
      <>
        <Alert
          compact
          level="info"
          title={t(`${patternName}.alertTitle`)}
          urgency="immediate"
        >
          {t(`${patternName}.alertDescription`)}
        </Alert>
        <div className="inline-grid grid-cols-[auto_auto_1fr] gap-md mt-md items-start">
          <div className="contents">
            <FormTextField
              readOnly
              value={fields[]?.value ? (mrField.value as string) : t('none')}
              label={t(`${patternName}.${id}${mrSuffix}`, { defaultValue: '' })}
              onChange={() => undefined}
            />
          </div>

          <IVPField
            id="manufacturer"
            mrSuffix="Mr_disp"
            patternName={patternName}
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="model"
            mrSuffix="Mr_disp"
            patternName={patternName}
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="type"
            mrSuffix="Mr_disp"
            patternName={patternName}
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="material"
            mrSuffix="Mr_disp"
            patternName={patternName}
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="connectionType"
            mrSuffix="Mr_disp"
            patternName={patternName}
            CorrectionElement={FormTextField}
          />
          <IVPField
            id="location"
            mrSuffix="Mr_disp"
            patternName={patternName}
            CorrectionElement={FormTextField}
          />
        </div>
      </>
    </fieldset>
  )
}

export default IVPOutletGateNotEquipped
