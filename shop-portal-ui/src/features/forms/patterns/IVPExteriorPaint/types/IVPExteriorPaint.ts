import { getOptions } from '@/features/forms/api/getOptions'
import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { mergeAll } from '../../../types/FormBranch'
import { mr, reply } from '../../types/MRData'

const requiredTextField = z.string().min(1, { message: 'Required' })
const PaintSystemOptions = options().async(() =>
  getOptions({ name: 'IVPExtPaintSystem' }),
)
const PaintColorOptions = options().async(() =>
  getOptions({ name: 'IVPExtPaintColor' }),
)

const WithYearPaintedReply = reply('yearPainted', requiredTextField)
const WithPaintedByReply = reply('paintedBy', z.string().max(4))
const WithPaintSystemReply = reply('paintSystem', PaintSystemOptions)
const WithPaintColorReply = reply('paintColor', PaintColorOptions)

const IVPExteriorPaint = mr({
  withData: merge(
    z.object({
      yearPainted_MR: z.string(),
      paintedBy_MR: z.string(),
      paintSystem_MR: z.string(),
      paintColor_MR: z.string(),
    }),
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      merge(
        z.object({
          allCorrect: No,
        }),
        mergeAll(
          WithYearPaintedReply,
          WithPaintedByReply,
          WithPaintSystemReply,
          WithPaintColorReply,
        ),
      ),
    ),
  ),
  withoutData: {
    yearPainted: z.string(),
    paintedBy: z.string().max(4),
    paintSystem: PaintSystemOptions,
    paintColor: PaintColorOptions,
  },
})
type IVPExteriorPaint = z.infer<typeof IVPExteriorPaint>

export { IVPExteriorPaint }
