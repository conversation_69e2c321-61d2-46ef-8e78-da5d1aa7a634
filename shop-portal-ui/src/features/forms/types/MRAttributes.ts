export enum MRAttribute {
  BLOCKED_OFF = 'BLOCKED_OFF',
  BUILDER_LOT = 'Builder Lot',
  COIL_DESIGN = 'COIL_DESIGN',
  COLOR = 'COLOR',
  CONFIGURATION = 'CONFIGURATION',
  CONNECTION_TYPE = 'CONNECTION_TYPE',
  CAPACITY = 'Capacity',
  CAR_BUILDER = 'Car Builder',
  CASTING_DATE = 'Casting_Date',
  EXT_PAINT_DATE_APPL = 'Ext Paint-Date Appl',
  EXT_PAINT_SHOP_APPL = 'Ext Paint-Shop Appl',
  FLOW_CAPACITY = 'FLOW_CAPACITY',
  ICL_DATE = 'ICL_Date',
  LOCATION = 'LOCATION',
  LIGHT_WEIGHT = 'Light Weight',
  LIGHTWEIGHT_DATE_APPL = 'Lightweight - Date Appl',
  LIGHTWEIGHT_SHOP_APPL = 'Lightweight - Shop Appl',
  LOAD_LIMIT = 'Load Limit',
  MANUFACTURER_NAME = 'MANUFACTURER_NAME',
  MANUFACTURER_PART_NUMBER = 'MANUFACTURER_PART_NUMBER',
  MATERIAL = 'MATERIAL',
  MATERIAL_TYPE = 'MATERIAL_TYPE',
  NOMINAL_SIZE = 'NOMINAL_SIZE',
  NUMBER_OF_LINES_OF_COIL = 'NUMBER_OF_LINES_OF_COIL',
  PAINT_SYSTEM = 'PAINT_SYSTEM',
  SHAPE = 'SHAPE',
  SPRING_GROUPING = 'SPRING_GROUPING',
  STYLE = 'STYLE',
  SHELL_CAPACITY = 'Shell Capacity',
  SHELL_CAPACITY_1 = 'Shell Capacity (1)',
  SHELL_CAPACITY_2 = 'Shell Capacity (2)',
  SHELL_CAPACITY_3 = 'Shell Capacity (3)',
  STAR_STENCIL = 'Star Stencil',
  STENCILED_CLASS_CAR_STYLE = 'Stenciled Class/Car Style',
  TQ_INT_LINING_DONE = 'TQ Int Lining Done',
  TQ_INT_LINING_DUE = 'TQ Int Lining Due',
  TQ_INT_LINING_SHOP = 'TQ Int Lining Shop',
  TQ_RULE_88B_DONE = 'TQ Rule 88B Done',
  TQ_RULE_88B_DUE = 'TQ Rule 88B Due',
  TQ_RULE_88B_SHOP = 'TQ Rule 88B Shop',
  TQ_SAFETY_VLV_DONE = 'TQ Safety Vlv Done',
  TQ_SAFETY_VLV_DUE = 'TQ Safety Vlv Due',
  TQ_SAFETY_VLV_SHOP = 'TQ Safety Vlv Shop',
  TQ_SERVICE_EQUIPMENT_DONE = 'TQ Service Equipment Done',
  TQ_SERVICE_EQUIPMENT_DUE = 'TQ Service Equipment Due',
  TQ_SERVICE_EQUIPMENT_SHOP = 'TQ Service Equipment Shop',
  TQ_STUB_SILL_DONE = 'TQ Stub Sill Done',
  TQ_STUB_SILL_DUE = 'TQ Stub Sill Due',
  TQ_STUB_SILL_SHOP = 'TQ Stub Sill Shop',
  TQ_TANK_QUALIFICATION_DONE = 'TQ Tank Qualification Done',
  TQ_TANK_QUALIFICATION_DUE = 'TQ Tank Qualification Due',
  TQ_TANK_QUALIFICATION_SHOP = 'TQ Tank Qualification Shop',
  TQ_TANK_THICKNESS_DONE = 'TQ Tank Thickness Done',
  TQ_TANK_THICKNESS_DUE = 'TQ Tank Thickness Due',
  TQ_TANK_THICKNESS_SHOP = 'TQ Tank Thickness Shop',
  TYPE = 'TYPE',
  TYPE_OF_BRAKES = 'TYPE_OF_BRAKES',
  TYPE_OF_COIL = 'TYPE_OF_COIL',
  TYPE_OF_INSULATION = 'TYPE_OF_INSULATION',
  TYPE_OF_PART = 'TYPE_OF_PART',
  TYPE_OF_TRUCK = 'TYPE_OF_TRUCK',
  UMLER_NUM_OF_OUTLETS_FREIGHT = 'UMLER_NUM_OF_OUTLETS_FREIGHT',
  AAR_CODE = 'AAR_CODE',
}

/**
 * Maps ATTRIBUTE_NAME to the actual {FIELD_NAME}_MR (current data) field of the pattern
 */
const MR_FIELD_BY_ATTRIBUTE: Record<string, string> = {
  [MRAttribute.LIGHT_WEIGHT]: 'lightWeight_MR',
  [MRAttribute.LIGHTWEIGHT_DATE_APPL]: 'lightWeightDate_MR',
  [MRAttribute.LIGHTWEIGHT_SHOP_APPL]: 'lightWeightBy_MR',
  [MRAttribute.BUILDER_LOT]: 'lotNumber_MR',
  [MRAttribute.CAR_BUILDER]: 'builderName_MR',
  [MRAttribute.PAINT_SYSTEM]: 'paintSystem_MR',
  [MRAttribute.EXT_PAINT_DATE_APPL]: 'yearPainted_MR',
  [MRAttribute.EXT_PAINT_SHOP_APPL]: 'paintedBy_MR',
  [MRAttribute.COLOR]: 'paintColor_MR',
  [MRAttribute.MANUFACTURER_NAME]: 'manufacturer_MR',
  [MRAttribute.MANUFACTURER_PART_NUMBER]: 'model_MR',
  [MRAttribute.TYPE]: 'type_MR',
  [MRAttribute.MATERIAL]: 'material_MR',
  [MRAttribute.CONNECTION_TYPE]: 'connectionType_MR',
  [MRAttribute.LOCATION]: 'location_MR',
}

function getMRFieldByAttribute(attribute: string): string {
  return MR_FIELD_BY_ATTRIBUTE[attribute] ?? 'currentData_MR'
}

/**
 * Maps ATTRIBUTE_NAME to the actual {FIELD_NAME} (correction) field of the pattern
 */

const MR_RESPONSE_FIELD_BY_ATTRIBUTE: Record<string, string> = {
  [MRAttribute.PAINT_SYSTEM]: 'paintSystem',
  [MRAttribute.EXT_PAINT_SHOP_APPL]: 'paintedBy',
  [MRAttribute.COLOR]: 'paintColor',
  [MRAttribute.LIGHT_WEIGHT]: 'lightWeight',
  [MRAttribute.LIGHTWEIGHT_SHOP_APPL]: 'lightWeightBy',
  [MRAttribute.BUILDER_LOT]: 'lotNumber',
  [MRAttribute.CAR_BUILDER]: 'builderName',
  [MRAttribute.TYPE]: 'type',
  [MRAttribute.MANUFACTURER_NAME]: 'manufacturer',
  [MRAttribute.MANUFACTURER_PART_NUMBER]: 'model',
  [MRAttribute.MATERIAL]: 'material',
  [MRAttribute.CONNECTION_TYPE]: 'connectionType',
  [MRAttribute.LOCATION]: 'location',
}

function getMRResponseFieldByAttribute(attribute: string): string {
  return MR_RESPONSE_FIELD_BY_ATTRIBUTE[attribute] ?? 'currentData'
}

export { getMRFieldByAttribute, getMRResponseFieldByAttribute }
