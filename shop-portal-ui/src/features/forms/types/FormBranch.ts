import { getValue, <PERSON><PERSON>, setValue } from '@/types/Json'
import {
  addIssueToContext,
  INVALID,
  ParseInput,
  ParseReturnType,
  z,
  ZodObject,
  ZodRawShape,
  ZodSchema,
  ZodType,
  ZodTypeAny,
  ZodTypeDef,
} from 'zod'
import {
  cloneSchema,
  getPropertySchema,
  isObjectBranch,
  isObjectSchema,
  MergeCommonKeys,
  mergeObjectSchemas,
  parseValid,
  Path,
} from '../utils/schema'
import { FormOptions } from './FormOptions'

function unroll<O extends BaseOptions>(S: FormBranch<O>): ZodTypeAny[] {
  return S.options().flatMap((S) => (isObjectBranch(S) ? unroll(S) : S))
}

type BaseOptions = Readonly<[ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]]>

type Definition<Options extends BaseOptions> = ZodTypeDef & {
  path: Path<Options[number]['_output']>
  options: Map<ZodSchema, Options[number]>
}

type ConstructorArgs<Options extends BaseOptions> = {
  path: Path<Options[number]['_output']>
  options: Options
}

class FormBranch<Options extends BaseOptions = BaseOptions> extends ZodType<
  Options[number]['_output'],
  Definition<Options>,
  Options[number]['_input']
> {
  constructor({ path, options }: ConstructorArgs<Options>) {
    const map = new Map<ZodSchema, Options[number]>(
      options.map((o) => {
        const Schemas = getPropertySchema(o as ZodSchema, path)

        if (isObjectSchema(o)) {
          const [Schema, ...rest] = Schemas

          if (rest.length) {
            throw new Error(
              `Ambiguous discriminator path ${JSON.stringify(path)}. (Object Option)`,
            )
          }

          return [Schema, o]
        }

        if (isObjectBranch(o)) {
          return [FormOptions.join(Schemas as FormOptions[]), o]
        }

        throw new Error('Schema not supported as FormBranch option', {
          cause: o,
        })
      }),
    )

    super({
      path,
      options: map,
    })
  }

  _parse(input: ParseInput): ParseReturnType<this['_output']> {
    if (!input.data) {
      const ctx = this._getOrReturnCtx(input)
      addIssueToContext(ctx, {
        code: 'custom',
        message: 'Required',
      })
      return INVALID
    }

    const schemas = this.options(input.data)

    if (schemas.length === 0) {
      /**
       * If there are not matching schemas, force parsing as regular union
       * to get the error message.
       */
      const union = z.union([...this._def.options.values()] as [
        Options[number],
        Options[number],
        ...Options,
      ])

      return union._parse(input)
    }

    if (schemas.length > 1) {
      const ctx = this._getOrReturnCtx(input)
      addIssueToContext(ctx, {
        code: 'custom',
        message: 'Multiple matching options',
      })
      return INVALID
    }

    const [Schema] = schemas

    return Schema._parse(input)
  }

  /**
   * Returns the schemas that match the input.
   * If no input is provided, returns all schemas.
   */
  options(input?: unknown): Options[number][] {
    const { path, options } = this._def

    if (!input) {
      return [...this._def.options.values()]
    }

    const json = Json.safeParse(input)

    if (!json.success) {
      return []
    }

    const value = getValue(json.data, path)

    if (!value) {
      return []
    }

    // Using the "find" function directly over the iterator is not supported
    const Discriminator = [...options.keys()].find(
      (S) => S.safeParse(value).success,
    )

    if (!Discriminator) {
      return []
    }

    const Schema = options.get(Discriminator)

    if (!Schema) {
      return []
    }

    return [Schema]
  }

  isDiscriminatorPath(path: readonly string[]) {
    return (
      path.length === this._def.path.length &&
      path.every((value, index) => value === this._def.path[index])
    )
  }

  getDiscriminatorJointSchema(): FormOptions {
    /**
     * The "options" map is indexed by the discriminator schema for each option.
     */
    return FormOptions.join([...this._def.options.keys()] as FormOptions[])
  }

  /**
   * Returns an array of potential schemas for a given property path within the schema.
   *
   * When traversing complex schemas that include union types (e.g., ZodUnion, ZodDiscriminatedUnion, FormBranch),
   * ambiguities can arise. This function handles such cases by returning a distinct schema for each branch of the union.
   * Supplying an input object that provides a value for the specified path can help narrow the ambiguity; if the input
   * is insufficient to determine a unique branch, the full list of possible schemas is returned.
   *
   * If the schema does not include union types along the path, the returned array contains a single schema.
   *
   * If the path terminates at the discriminator of a FormBranch, a single consolidated FormOptions schema is returned,
   * aggregating one option per branch of the FormBranch.
   *
   * When the path traverses only FormBranch union types, the input object may be used to resolve the ambiguity.
   * In this scenario, if the input validates against exactly one branch, only that schema is returned; if it validates
   * against none, an empty array is returned. However, if the input would validate against multiple branches, an error
   * is thrown, as FormBranch schemas must resolve to a single valid branch.
   *
   * @param Schema The schema to traverse.
   * @param path The property path to resolve.
   * @param input (Optional) An input object used to disambiguate the schema based on the value at the specified path.
   * @returns An array of schemas corresponding to the property at the end of the path.
   */
  getPropertySchema(path: readonly string[], input?: unknown): ZodSchema[] {
    if (this.isDiscriminatorPath(path)) {
      return [this.getDiscriminatorJointSchema()]
    }

    let options = this.options(input)

    if (input && options.length > 1) {
      throw new Error('Multiple matching options')
    }

    if (input && !options.length) {
      options = this.options()
    }

    return options.flatMap((S) => getPropertySchema(S, path, input))
  }

  /**
   * Parses an object.
   * Does not throw if the object is invalid. Instead, only the valid properties are returned.
   * If the input is not valid for a single option, parses the input as a union of all options, considering nested branches.
   *
   * @param input The data to parse.
   * @returns The parsed data.
   */
  parseValid(input: unknown): unknown {
    const result = this.safeParse(input)

    if (result.success) {
      return result.data
    }

    const schemas = this.options(input)

    if (schemas.length !== 1) {
      const [o1, o2, ...options] = unroll(this)

      if (!o1 || !o2) {
        return {}
      }

      return parseValid(z.union([o1, o2, ...options]), input) ?? {}
    }

    const [Schema] = schemas

    const json = Json.safeParse(input)

    if (!json.success) {
      return {}
    }

    return setValue(
      parseValid(Schema, input) as Record<string, any>,
      this._def.path,
      getValue(json.data, this._def.path) ?? null,
    )
  }

  clone(): FormBranch<Options> {
    const branchOptions = this._def.options.entries().map(([, value]) => value)

    return new FormBranch({
      path: this._def.path,
      options: branchOptions.map((S) => cloneSchema(S)) as unknown as Options,
    })
  }

  static create<
    Options extends Readonly<[ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]]>,
  >(path: Path<Options[number]['_output']>, ...options: Options) {
    return new FormBranch({
      path,
      options,
    })
  }
}

export default function branch<
  Options extends Readonly<[ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]]>,
>(path: Path<Options[number]['_output']>, ...options: Options) {
  return FormBranch.create(path, ...options)
}

type ExtendObject<S extends ZodRawShape, E extends ZodRawShape> = ZodObject<
  MergeCommonKeys<S, E>
>

type ExtendBranch<Os extends BaseOptions, E extends ZodRawShape> = FormBranch<
  ExtendOptions<Os, E>
>

type ExtendOption<Option, BaseShape extends ZodRawShape> =
  Option extends ZodObject<infer S>
    ? ExtendObject<S, BaseShape>
    : Option extends FormBranch<infer InnerOptions>
      ? ExtendBranch<InnerOptions, BaseShape>
      : never

type ExtendOptions<
  Options extends readonly any[],
  BaseShape extends ZodRawShape,
> = {
  [K in keyof Options]: ExtendOption<Options[K], BaseShape>
}

/**
 * Merges a base Zod object schema with a FormBranch’s options.
 *
 * This function takes a base Zod object schema and a FormBranch that holds a tuple of schema options,
 * and produces a new FormBranch where each option is extended by merging in the properties from the base schema.
 *
 * The merge process applies the following logic for each option schema:
 * - **Object Schema Options:**
 *   - Computes the union of keys from both the base and option schemas.
 *   - For each key:
 *     - If **both** the base and option properties are object schemas, their shapes are merged using `mergeObjectSchemas()`.
 *     - If only one is an object schema (and the other is not an instance of `ZodNever`), the object schema is preserved.
 *     - If both properties exist but neither is an object schema, the resulting property is set to `Remove`.
 *   - Finally, the base schema is merged into the option and extended with the computed shape.
 * - **FormBranch Options:**
 *   - If an option is itself a FormBranch, the merge is applied recursively.
 * - **Error Handling:**
 *   - If an option is encountered that is neither a supported object schema nor a FormBranch, an error is thrown.
 *
 *
 * @param base - The base Zod object schema whose shape will be merged into each branch option.
 * @param branch - The FormBranch instance containing additional schema options to merge with the base.
 *
 * @returns A new FormBranch with schema options extended by the base schema.
 *
 * @throws If a branch option is encountered that is not a supported schema (i.e., neither a Zod object schema nor a FormBranch).
 */

function merge<
  BaseShape extends ZodRawShape,
  Options extends Readonly<[ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]]>,
>(
  base: ZodObject<BaseShape>,
  branch: FormBranch<Options>,
): FormBranch<ExtendOptions<Options, BaseShape>> {
  const opts = branch.options().map((S) => {
    if (isObjectSchema(S)) {
      return mergeObjectSchemas(base, S)
    }

    if (isObjectBranch(S)) {
      return merge(base, S)
    }

    throw new Error('FormBranch has unsupported Schema as Option')
  }) as unknown as Options

  return FormBranch.create(branch._def.path, ...opts) as unknown as FormBranch<
    ExtendOptions<Options, BaseShape>
  >
}

type GenericBranch = FormBranch<[ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]]>

// FIXME: this function does not have good type inference
function mergeAll(...branches: FormBranch<any>[]): GenericBranch {
  const [first, ...rest] = branches.reverse()

  return rest.reduce((acc, curr) => {
    const opts = curr.options().map((S) => {
      if (isObjectSchema(S)) {
        return merge(S, acc)
      }

      throw new Error('FormBranch has unsupported Schema as Option')
    }) as unknown as Readonly<[ZodTypeAny, ZodTypeAny, ...ZodTypeAny[]]>

    return FormBranch.create(curr._def.path, ...opts)
  }, first) as unknown as GenericBranch
}

export { FormBranch, merge, mergeAll }
