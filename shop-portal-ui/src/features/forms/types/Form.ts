import {
  FormResponsePattern,
  RESPONSE_SCHEMA_BY_PATTERN,
} from '@/features/forms/types/FormResponsePattern'
import { RailcarMRAttributes } from '@/features/railcars/types/Railcar'
import { Json } from '@/types/Json'
import { i18n } from '@gatx-corp/platform-one-common'
import { z, ZodSchema } from 'zod'
import { getCleaningConfirmationSignables } from '../templates/CleaningConfirmation/types/CleaningConfirmation'
import { getInbdExteriorCoatingInspSignables } from '../templates/InbdExteriorCoatingInsp/types/InbdExteriorCoatingInsp'
import { getInboundInspectionSignables } from '../templates/InboundInspection/types/InboundInspection'
import { cloneSchema, ObjectSchema, resolveSchema } from '../utils/schema'
import branch from './FormBranch'
import { FALSE, TRUE } from './FormSchemaConstants'
import {
  getMRFieldByAttribute,
  getMRResponseFieldByAttribute,
} from './MRAttributes'

enum FormTemplate {
  INBOUND_INSPECTION = 'Inbound Inspection',
  INTERIOR_LINING = 'Interior Lining',
  BENCHMARK_THICKNESS = 'Benchmark Thickness',
  DEFECT_THICKNESS = 'Defect Thickness',
  AFTER_REPAIR_THICKNESS = 'After-Repair Thickness',
  CERTIFICATE_OF_TEST = 'Certificate Of Test',
  R1 = 'R-1',
  INITIAL_SERVICE_EQUIPMENT = 'Initial Service Equipment',
  NITROGEN_PAD_APPLICATION = 'Nitrogen Pad Application',
  FINAL_SERVICE_EQUIPMENT = 'Final Service Equipment',
  CLEANING_CONFIRMATION = 'Cleaning Confirmation',
  STRUCTURAL_INTEGRITY = 'Structural Integrity',
  NDT_IR = 'NDT-IR',
  COATING_APPLICATION = 'Coating Application',
  COIL_TEST = 'Coil Test',
  INTERIOR_SPRAY_COATING_RUBBER_LINING_REPAIR_NEW_RUBBER_LINING = 'Interior Spray Coating_Rubber Lining Repair_New Rubber Lining',
  INBD_INTERIOR_LINING_COATING_INSP = 'Inbd Interior Lining/Coating Insp',
  INBD_INTERIOR_LINING_COATING_INSP2 = 'Inbd Interior Lining_Coating Insp',
  CS_LINING_COATING = 'CS Lining Coating',
  COT_PREPOP = 'COT Prepop',
  R1_PREPOP = 'R-1 Prepop',
  INBD_EXT_COATING_INSP = 'Inbd Exterior Coating Insp',
  FM_AFFIRMATION = 'FM Affirmation',
}

enum FormStatusCode {
  NOT_STARTED = 'NW',
  IN_PROCESS = 'IP',
  SIGNED = 'CM',
  DEACTIVATED = 'IN',
  ARCHIVED = 'AR',
}

type FormQuestionGrouping = {
  id: number
  code: string | null
  name: string | null
}

type ZoneLocation = {
  code: string
  name: string
  type: 'Interior' | 'Exterior'
}

type RelatedFormItem = {
  id: string
  name: string
  status: FormStatusCode
  sequence: number
}

enum ValidationCheck {
  DECAL_PRESENT = 'DecalPresent',
  DECAL_NOT_PRESENT = 'DecalNotPresent',
  CASTING_OUTDATED = 'CastingOutdated',
}

type FormResponseOption = {
  id: number
  name: string
  pattern: FormResponsePattern
  isDefaultPrintable: boolean
  checks: ValidationCheck[]
}

type MRAttribute = {
  id: string
  zoneLocationCode: string
  mrLocationCode: string[]
}

type FormQuestion = {
  id: number
  dciId: number
  zone: ZoneLocation | null
  question: string
  detail: string | null
  type: string | null
  inspMethod: string | null
  cutoutZone: string | null
  partTypeCode: string | null
  responseOptions: FormResponseOption[]
  groupId: string | null
  sectionId: string | null
  mrAttributes: MRAttribute[]
}

type Form = {
  id: string
  namePartOne: string
  namePartTwo: string | null
  shortName: string
  template: FormTemplate
  statusCode: FormStatusCode
  num: string
  sections: FormQuestionGrouping[]
  groups: FormQuestionGrouping[]
  questions: FormQuestion[]
  deactivatedReasonCode: string | null
  reasonForArch: string | null
  version: number
  clientVersion: string | null
  htmlExport: string | null
  relatedForms: RelatedFormItem[]
  sequence: number
}

const FormQuestionResponse = z.object({
  questionId: z.number(),
  responseId: z.number().nullable(),
  data: Json,
  comments: z.string().array().default([]),
})

type FormQuestionResponse = z.infer<typeof FormQuestionResponse>

const getFormQuestionResponseSchemas = async (form: Form) => {
  const result: Record<number, Record<number, ZodSchema>> = {}

  for (const question of form.questions) {
    result[question.id] = {}

    for (const option of question.responseOptions) {
      const Schema = cloneSchema(
        RESPONSE_SCHEMA_BY_PATTERN[option.pattern] ?? z.never(),
      )

      await resolveSchema(Schema, {
        questionId: question.id,
        responseId: option.id,
      })

      result[question.id][option.id] = Schema
    }
  }

  return result
}

async function getFormResponseSchema(form: Form) {
  const schemas = await getFormQuestionResponseSchemas(form)

  return z.object(
    Object.fromEntries(
      form.questions.map((q) => [
        q.id,
        branch(
          ['responseId'],
          ...(q.responseOptions.map((o) =>
            z.object({
              questionId: z.literal(q.id),
              responseId: z.literal(o.id),
              data: schemas[q.id][o.id].default(null),
              comments: z.string().array().default([]),
            }),
          ) as unknown as [ObjectSchema, ObjectSchema, ...ObjectSchema[]]),
          z.object({
            questionId: z.literal(q.id),
            responseId: z.null(),
            data: z.null(),
            comments: z.never().array().default([]),
          }),
        ),
      ]),
    ),
  )
}

function getQuestionMRData(
  question: FormQuestion,
  mrAttrsByLocation: Record<string, RailcarMRAttributes[]>,
) {
  if (!question.mrAttributes?.length) return null

  /**
   * Get MR attribute ids for the question
   */
  const questionAttrIds = new Set(question.mrAttributes.map((attr) => attr.id))

  /**
   * Get MR attributes for the question
   */
  const questionAttrsByLocation = {} as Record<
    string,
    FormQuestion['mrAttributes']
  >

  for (const attr of question.mrAttributes) {
    for (const mrLocationCode of attr.mrLocationCode) {
      if (!questionAttrsByLocation[mrLocationCode]) {
        questionAttrsByLocation[mrLocationCode] = []
      } else {
        questionAttrsByLocation[mrLocationCode].push(attr)
      }
    }
  }

  /**
   * Compose MR data for the question
   */

  const questionMRData = {} as Record<string, Record<string, string>>

  for (const locnCode in questionAttrsByLocation) {
    const attrs = mrAttrsByLocation[locnCode]?.filter(
      (attr) =>
        (!question.partTypeCode ||
          attr.partTypeCode === question.partTypeCode) &&
        attr.attributeId &&
        questionAttrIds.has(attr.attributeId.toString()),
    )

    if (attrs?.length) {
      questionMRData[locnCode] = Object.fromEntries([
        ...attrs.map((attr): [string, string] => [
          getMRFieldByAttribute(attr.attributeName),
          attr.attributeValue,
        ]),
        ...attrs.map((attr): [string, string] => [
          getMRResponseFieldByAttribute(attr.attributeName),
          attr.attributeValue,
        ]),
      ])

      questionMRData[locnCode].hasMRData = TRUE
    }
  }

  return Object.values(questionMRData) as (Record<string, string> & {
    hasMRData: 'true' | 'false'
  })[]
}

function getFormMRData(
  form: Form,
  mrAttrsByLocation: Record<string, RailcarMRAttributes[]>,
) {
  return Object.fromEntries(
    form.questions.map((q) => [q.id, getQuestionMRData(q, mrAttrsByLocation)]),
  )
}

type FormMRData = Record<string, Record<string, string>[] | null>

function getInitialFormQuestionResponse(
  question: FormQuestion,
  mrData: FormMRData,
) {
  let data = null

  const mr = mrData[question.id]

  if (question.mrAttributes.length && mr) {
    if (mr.length > 1) {
      data = mr
    } else if (mr.length === 1) {
      data = mr[0]
    } else {
      data = { hasMRData: FALSE }
    }
  }

  return data
}

function getInitialFormResponse(form: Form, mrData: FormMRData): FormResponse {
  return Object.fromEntries(
    form.questions.map((q) => [
      q.id,
      {
        questionId: q.id,
        responseId: null,
        data: getInitialFormQuestionResponse(q, mrData),
        comments: [],
      },
    ]),
  )
}

function getInitialFormContent(form: Form, mrData: FormMRData) {
  return {
    response: getInitialFormResponse(form, mrData),
    signatures: {},
    signatureHistory: {},
    version: 0,
    createdDate: null,
    lastModifiedDate: null,
  }
}

function getFormContent(
  form: Form,
  content: FormContent | undefined,
  mrData: FormMRData,
): FormContent {
  const base = getInitialFormContent(form, mrData)

  return {
    ...base,
    ...content,
    response: Object.fromEntries(
      Object.entries(base.response).map(([key, value]) => [
        key,
        { ...value, ...content?.response[key] },
      ]),
    ),
  }
}

const FormSignatureType = z.enum(['complete', 'deferred'])
const FormSignatureHistoryEntryType = z.union([
  FormSignatureType,
  z.enum(['unsign']),
])

const FormSignature = z.object({
  signedBy: z.object({ id: z.string(), name: z.string() }),
  signedAt: z.string().datetime(),
  extractionKey: z.string().default('signedBy'),
  extractionKeyDateLabel: z.string().default('signedAt'),
  type: FormSignatureType,
})

const FormSignatureHistoryEntry = z.record(z.any()).and(
  z.object({
    action: FormSignatureHistoryEntryType,
  }),
)

const FormSignatureHistory = z.record(
  FormSignatureHistoryEntry.array().default([]),
)

type SignatureMetadata = {
  extractionKey: string
  extractionKeyDateLabel?: string
}

const EXTRACTION_KEYS_BY_SIGNABLE: Record<string, SignatureMetadata> = {
  cleaningApproval: {
    extractionKey: 'approvedBy',
    extractionKeyDateLabel: 'approvedDate',
  },
  cleaningInformation: {
    extractionKey: 'performedBy',
    extractionKeyDateLabel: 'cleaningPerformedDate',
  },
  cleaningQA: {
    extractionKey: 'verifiedBy',
    extractionKeyDateLabel: 'verificationDate',
  },
}

function createFormSignature(
  signableId: string,
  signedBy: { id: string; name: string },
  type: FormSignatureType,
): FormSignature {
  const signedAt = new Date().toISOString()

  return FormSignature.parse({
    signedBy,
    signedAt,
    type,
    ...EXTRACTION_KEYS_BY_SIGNABLE[signableId],
  })
}

type GetSignables = (form: Form, response: FormResponse) => string[]

const SIGNABLES_GETTER_BY_TEMPLATE: Partial<
  Record<FormTemplate, GetSignables>
> = {
  [FormTemplate.CLEANING_CONFIRMATION]: getCleaningConfirmationSignables,
  [FormTemplate.INBOUND_INSPECTION]: getInboundInspectionSignables,
  [FormTemplate.INBD_EXT_COATING_INSP]: getInbdExteriorCoatingInspSignables,
}

function getFormSignables(...args: Parameters<GetSignables>) {
  return (
    SIGNABLES_GETTER_BY_TEMPLATE[args[0].template]?.(...args) ?? ['unknown']
  )
}

function getDefaultResponseOption(question: FormQuestion) {
  return question.responseOptions.length === 1
    ? question.responseOptions[0]
    : null
}

function getResponseOptionById(question: FormQuestion, responseId: number) {
  return question.responseOptions.find(({ id }) => responseId === id) ?? null
}

function getSelectedResponseOption(
  question: FormQuestion,
  response: FormResponse,
) {
  const responseId = response[question.id].responseId

  return responseId
    ? getResponseOptionById(question, responseId)
    : getDefaultResponseOption(question)
}

const FormResponse = z.record(FormQuestionResponse)
const FormSignatures = z.record(FormSignature.optional())

const FormContent = z.object({
  response: FormResponse,
  signatures: FormSignatures,
  signatureHistory: FormSignatureHistory,
  version: z.number(),
  createdDate: z.string().nullish(),
  lastModifiedDate: z.string().nullish(),
})

type FormSignature = z.infer<typeof FormSignature>
type FormSignatureType = z.infer<typeof FormSignatureType>
type FormSignatureHistory = z.infer<typeof FormSignatureHistory>
type FormSignatureHistoryEntryType = z.infer<
  typeof FormSignatureHistoryEntryType
>
type FormSignatureHistoryEntry = z.infer<typeof FormSignatureHistoryEntry>

type FormSignatures = z.infer<typeof FormSignatures>
type FormResponse = z.infer<typeof FormResponse>
type FormContent = z.infer<typeof FormContent>

const GLOBAL_FORM_SIGNATURE_ID = 'form'

const VALIDATION_CHECKS: Record<ValidationCheck, (context: Json) => boolean> = {
  DecalPresent: () => true,
  DecalNotPresent: () => true,
  CastingOutdated: (context: Json) => {
    const currentYear = new Date().getFullYear()
    const currentYearTwoDigit = currentYear.toString().slice(2, 4)
    const castingYear = (context as unknown as { castingYear?: string })
      .castingYear

    if (!castingYear) return false

    const fullCastingYear =
      castingYear <= currentYearTwoDigit
        ? 2000 + Number(castingYear)
        : 1900 + Number(castingYear)

    if (currentYear - fullCastingYear >= 45) {
      return true
    }

    return false
  },
}

function getCommentsFromChecks(checks: ValidationCheck[], context: Json) {
  return checks
    .filter((check) => VALIDATION_CHECKS[check](context))
    .map((check) => i18n.t(`checks.${check}`, { ns: 'Forms', lng: 'en' }))
}

export {
  createFormSignature,
  FormContent,
  FormQuestionResponse,
  FormResponse,
  FormSignature,
  FormSignatureHistory,
  FormSignatureHistoryEntry,
  FormSignatureHistoryEntryType,
  FormSignatures,
  FormSignatureType,
  FormStatusCode,
  FormTemplate,
  getCommentsFromChecks,
  getFormContent,
  getFormMRData,
  getFormResponseSchema,
  getFormSignables,
  getInitialFormContent,
  getInitialFormQuestionResponse,
  getSelectedResponseOption,
  GLOBAL_FORM_SIGNATURE_ID,
  type Form,
  type FormMRData,
  type FormQuestion,
  type FormResponseOption,
  type RelatedFormItem,
}
