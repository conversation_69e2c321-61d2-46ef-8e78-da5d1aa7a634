import {
  FormResponsePattern,
  RESPONSE_SCHEMA_BY_PATTERN,
} from '@/features/forms/types/FormResponsePattern'
import { RailcarMRAttributes } from '@/features/railcars/types/Railcar'
import { Json } from '@/types/Json'
import { i18n } from '@gatx-corp/platform-one-common'
import { z, ZodSchema } from 'zod'
import { getCleaningConfirmationSignables } from '../templates/CleaningConfirmation/types/CleaningConfirmation'
import { getInbdExteriorCoatingInspSignables } from '../templates/InbdExteriorCoatingInsp/types/InbdExteriorCoatingInsp'
import { getInboundInspectionSignables } from '../templates/InboundInspection/types/InboundInspection'
import { cloneSchema, ObjectSchema, resolveSchema } from '../utils/schema'
import branch from './FormBranch'
import { FALSE, TRUE } from './FormSchemaConstants'
import {
  getMRFieldByAttribute,
  getMRResponseFieldByAttribute,
} from './MRAttributes'

enum FormTemplate {
  INBOUND_INSPECTION = 'Inbound Inspection',
  INTERIOR_LINING = 'Interior Lining',
  BENCHMARK_THICKNESS = 'Benchmark Thickness',
  DEFECT_THICKNESS = 'Defect Thickness',
  AFTER_REPAIR_THICKNESS = 'After-Repair Thickness',
  CERTIFICATE_OF_TEST = 'Certificate Of Test',
  R1 = 'R-1',
  INITIAL_SERVICE_EQUIPMENT = 'Initial Service Equipment',
  NITROGEN_PAD_APPLICATION = 'Nitrogen Pad Application',
  FINAL_SERVICE_EQUIPMENT = 'Final Service Equipment',
  CLEANING_CONFIRMATION = 'Cleaning Confirmation',
  STRUCTURAL_INTEGRITY = 'Structural Integrity',
  NDT_IR = 'NDT-IR',
  COATING_APPLICATION = 'Coating Application',
  COIL_TEST = 'Coil Test',
  INTERIOR_SPRAY_COATING_RUBBER_LINING_REPAIR_NEW_RUBBER_LINING = 'Interior Spray Coating_Rubber Lining Repair_New Rubber Lining',
  INBD_INTERIOR_LINING_COATING_INSP = 'Inbd Interior Lining/Coating Insp',
  INBD_INTERIOR_LINING_COATING_INSP2 = 'Inbd Interior Lining_Coating Insp',
  CS_LINING_COATING = 'CS Lining Coating',
  COT_PREPOP = 'COT Prepop',
  R1_PREPOP = 'R-1 Prepop',
  INBD_EXT_COATING_INSP = 'Inbd Exterior Coating Insp',
  FM_AFFIRMATION = 'FM Affirmation',
}

enum FormStatusCode {
  NOT_STARTED = 'NW',
  IN_PROCESS = 'IP',
  SIGNED = 'CM',
  DEACTIVATED = 'IN',
  ARCHIVED = 'AR',
}

type FormQuestionGrouping = {
  id: number
  code: string | null
  name: string | null
}

type ZoneLocation = {
  code: string
  name: string
  type: 'Interior' | 'Exterior'
}

type RelatedFormItem = {
  id: string
  name: string
  status: FormStatusCode
  sequence: number
}

enum ValidationCheck {
  DECAL_PRESENT = 'DecalPresent',
  DECAL_NOT_PRESENT = 'DecalNotPresent',
  CASTING_OUTDATED = 'CastingOutdated',
}

type FormResponseOption = {
  id: number
  name: string
  pattern: FormResponsePattern
  isDefaultPrintable: boolean
  checks: ValidationCheck[]
}

type MRAttribute = {
  id: string
  zoneLocationCode: string
  mrLocationCode: string[]
}

type FormQuestion = {
  id: number
  dciId: number
  zone: ZoneLocation | null
  question: string
  detail: string | null
  type: string | null
  inspMethod: string | null
  cutoutZone: string | null
  partTypeCode: string | null
  responseOptions: FormResponseOption[]
  groupId: string | null
  sectionId: string | null
  mrAttributes: MRAttribute[]
}

type Form = {
  id: string
  namePartOne: string
  namePartTwo: string | null
  shortName: string
  template: FormTemplate
  statusCode: FormStatusCode
  num: string
  sections: FormQuestionGrouping[]
  groups: FormQuestionGrouping[]
  questions: FormQuestion[]
  deactivatedReasonCode: string | null
  reasonForArch: string | null
  version: number
  clientVersion: string | null
  htmlExport: string | null
  relatedForms: RelatedFormItem[]
  sequence: number
}

const FormQuestionResponse = z.object({
  questionId: z.number(),
  responseId: z.number().nullable(),
  data: Json,
  comments: z.string().array().default([]),
})

type FormQuestionResponse = z.infer<typeof FormQuestionResponse>

const getFormQuestionResponseSchemas = async (form: Form) => {
  const result: Record<number, Record<number, ZodSchema>> = {}

  for (const question of form.questions) {
    result[question.id] = {}

    for (const option of question.responseOptions) {
      const Schema = cloneSchema(
        RESPONSE_SCHEMA_BY_PATTERN[option.pattern] ?? z.never(),
      )

      await resolveSchema(Schema, {
        questionId: question.id,
        responseId: option.id,
      })

      result[question.id][option.id] = Schema
    }
  }

  return result
}

async function getFormResponseSchema(form: Form) {
  const schemas = await getFormQuestionResponseSchemas(form)

  return z.object(
    Object.fromEntries(
      form.questions.map((q) => [
        q.id,
        branch(
          ['responseId'],
          ...(q.responseOptions.map((o) =>
            z.object({
              questionId: z.literal(q.id),
              responseId: z.literal(o.id),
              data: schemas[q.id][o.id].default(null),
              comments: z.string().array().default([]),
            }),
          ) as unknown as [ObjectSchema, ObjectSchema, ...ObjectSchema[]]),
          z.object({
            questionId: z.literal(q.id),
            responseId: z.null(),
            data: z.null(),
            comments: z.never().array().default([]),
          }),
        ),
      ]),
    ),
  )
}

function getInitialData(
  question: FormQuestion,
  mrAttributes: RailcarMRAttributes[],
): Json {
  if (!question.mrAttributes?.length) return null

  const questionAttributes = mrAttributes?.filter((attr) =>
    // TODO: validate that this filter is required
    // attr.partTypeCode === question.partTypeCode &&
    question.mrAttributes.some(
      (questionAttr) =>
        questionAttr.id === `${attr.attributeId}` &&
        questionAttr.mrLocationCode.includes(attr.mrLocationCode),
    ),
  )

  const currentMRData = Object.fromEntries(
    questionAttributes
      ? questionAttributes.map((attr) => [
          getMRFieldByAttribute(attr.attributeName),
          attr.attributeValue,
        ])
      : [],
  )

  const hasMRData = questionAttributes?.length ? TRUE : FALSE

  const defaultResponse = hasMRData
    ? Object.fromEntries(
        questionAttributes.map((attr) => [
          getMRResponseFieldByAttribute(attr.attributeName),
          attr.attributeValue,
        ]),
      )
    : {}

  return {
    hasMRData,
    ...currentMRData,
    ...defaultResponse,
  }
}

function getInitialFormResponse(
  form: Form,
  mrAttributes: RailcarMRAttributes[],
): FormResponse {
  return Object.fromEntries(
    form.questions.map((q) => [
      q.id,
      {
        questionId: q.id,
        responseId: null,
        data: getInitialData(q, mrAttributes),
        comments: [],
      },
    ]),
  )
}

function getInitialFormContent(
  form: Form,
  mrAttributes: RailcarMRAttributes[],
) {
  return {
    response: getInitialFormResponse(form, mrAttributes),
    signatures: {},
    signatureHistory: {},
    version: 0,
    createdDate: null,
    lastModifiedDate: null,
  }
}

function getFormContent(
  form: Form,
  content: FormContent | undefined,
  mrAttributes: RailcarMRAttributes[],
): FormContent {
  const base = getInitialFormContent(form, mrAttributes)

  return {
    ...base,
    ...content,
    response: Object.fromEntries(
      Object.entries(base.response).map(([key, value]) => [
        key,
        { ...value, ...content?.response[key] },
      ]),
    ),
  }
}

const FormSignatureType = z.enum(['complete', 'deferred'])
const FormSignatureHistoryEntryType = z.union([
  FormSignatureType,
  z.enum(['unsign']),
])

const FormSignature = z.object({
  signedBy: z.object({ id: z.string(), name: z.string() }),
  signedAt: z.string().datetime(),
  extractionKey: z.string().default('signedBy'),
  extractionKeyDateLabel: z.string().default('signedAt'),
  type: FormSignatureType,
})

const FormSignatureHistoryEntry = z.record(z.any()).and(
  z.object({
    action: FormSignatureHistoryEntryType,
  }),
)

const FormSignatureHistory = z.record(
  FormSignatureHistoryEntry.array().default([]),
)

type SignatureMetadata = {
  extractionKey: string
  extractionKeyDateLabel?: string
}

const EXTRACTION_KEYS_BY_SIGNABLE: Record<string, SignatureMetadata> = {
  cleaningApproval: {
    extractionKey: 'approvedBy',
    extractionKeyDateLabel: 'approvedDate',
  },
  cleaningInformation: {
    extractionKey: 'performedBy',
    extractionKeyDateLabel: 'cleaningPerformedDate',
  },
  cleaningQA: {
    extractionKey: 'verifiedBy',
    extractionKeyDateLabel: 'verificationDate',
  },
}

function createFormSignature(
  signableId: string,
  signedBy: { id: string; name: string },
  type: FormSignatureType,
): FormSignature {
  const signedAt = new Date().toISOString()

  return FormSignature.parse({
    signedBy,
    signedAt,
    type,
    ...EXTRACTION_KEYS_BY_SIGNABLE[signableId],
  })
}

type GetSignables = (form: Form, response: FormResponse) => string[]

const SIGNABLES_GETTER_BY_TEMPLATE: Partial<
  Record<FormTemplate, GetSignables>
> = {
  [FormTemplate.CLEANING_CONFIRMATION]: getCleaningConfirmationSignables,
  [FormTemplate.INBOUND_INSPECTION]: getInboundInspectionSignables,
  [FormTemplate.INBD_EXT_COATING_INSP]: getInbdExteriorCoatingInspSignables,
}

function getFormSignables(...args: Parameters<GetSignables>) {
  return (
    SIGNABLES_GETTER_BY_TEMPLATE[args[0].template]?.(...args) ?? ['unknown']
  )
}

function getDefaultResponseOption(question: FormQuestion) {
  return question.responseOptions.length === 1
    ? question.responseOptions[0]
    : null
}

function getResponseOptionById(question: FormQuestion, responseId: number) {
  return question.responseOptions.find(({ id }) => responseId === id) ?? null
}

function getSelectedResponseOption(
  question: FormQuestion,
  response: FormResponse,
) {
  const responseId = response[question.id].responseId

  return responseId
    ? getResponseOptionById(question, responseId)
    : getDefaultResponseOption(question)
}

const FormResponse = z.record(FormQuestionResponse)
const FormSignatures = z.record(FormSignature.optional())

const FormContent = z.object({
  response: FormResponse,
  signatures: FormSignatures,
  signatureHistory: FormSignatureHistory,
  version: z.number(),
  createdDate: z.string().nullish(),
  lastModifiedDate: z.string().nullish(),
})

type FormSignature = z.infer<typeof FormSignature>
type FormSignatureType = z.infer<typeof FormSignatureType>
type FormSignatureHistory = z.infer<typeof FormSignatureHistory>
type FormSignatureHistoryEntryType = z.infer<
  typeof FormSignatureHistoryEntryType
>
type FormSignatureHistoryEntry = z.infer<typeof FormSignatureHistoryEntry>

type FormSignatures = z.infer<typeof FormSignatures>
type FormResponse = z.infer<typeof FormResponse>
type FormContent = z.infer<typeof FormContent>

const GLOBAL_FORM_SIGNATURE_ID = 'form'

const VALIDATION_CHECKS: Record<ValidationCheck, (context: Json) => boolean> = {
  DecalPresent: () => true,
  DecalNotPresent: () => true,
  CastingOutdated: (_context: Json) => true,
}

function getCommentsFromChecks(checks: ValidationCheck[], context: Json) {
  return checks
    .filter((check) => VALIDATION_CHECKS[check](context))
    .map((check) => i18n.t(`checks.${check}`, { ns: 'Forms', lng: 'en' }))
}

export {
  createFormSignature,
  FormContent,
  FormQuestionResponse,
  FormResponse,
  FormSignature,
  FormSignatureHistory,
  FormSignatureHistoryEntry,
  FormSignatureHistoryEntryType,
  FormSignatures,
  FormSignatureType,
  FormStatusCode,
  FormTemplate,
  getCommentsFromChecks,
  getFormContent,
  getFormResponseSchema,
  getFormSignables,
  getInitialData,
  getInitialFormContent,
  getSelectedResponseOption,
  GLOBAL_FORM_SIGNATURE_ID,
}
export type { Form, FormQuestion, FormResponseOption, RelatedFormItem }
