import { <PERSON><PERSON> } from '@/types/Json'
import {
  createContext,
  PropsWithChildren,
  SetStateAction,
  use,
  useCallback,
  useMemo,
} from 'react'
import { ZodSchema } from 'zod'
import {
  FormContent,
  FormQuestion,
  FormResponseOption,
  getCommentsFromChecks,
  getInitialFormQuestionResponse,
  getSelectedResponseOption,
} from '../types/Form'
import { FormResponsePattern } from '../types/FormResponsePattern'
import { invariant, StateDuple } from '../utils'
import {
  useFormMRData,
  useFormQuestionPatternSchema,
  useFormResponse,
} from './FormProvider'

const FormQuestionSpecContext = createContext<FormQuestion | undefined>(
  undefined,
)

const FormQuestionResponseContext = createContext<StateDuple<Json> | undefined>(
  undefined,
)

const FormResponseOptionContext = createContext<
  StateDuple<FormResponseOption | null> | undefined
>(undefined)

type Props = PropsWithChildren<{
  question: FormQuestion
  /**
   * FIXME: onPatternChange should not expect the form's response. See comment in usage below.
   */
  onPatternChange?: (
    p: FormResponsePattern | null,
    r: FormContent['response'],
  ) => void
}>

const FormQuestionProvider = ({
  children,
  question,
  onPatternChange,
}: Props) => {
  const [formResponse, setFormResponse] = useFormResponse()
  const option = getSelectedResponseOption(question, formResponse)

  const mrData = useFormMRData()

  const response = useMemo(
    () => formResponse[question.id].data ?? {},
    [formResponse, question.id],
  )

  const setResponse = useCallback(
    async (action: SetStateAction<Json>) => {
      if (!option) {
        throw new Error('No response option selected')
      }

      const data = typeof action === 'function' ? action(response) : action

      await setFormResponse((prev) => ({
        ...prev,
        [question.id]: {
          questionId: question.id,
          responseId: option.id,
          data,
          comments: getCommentsFromChecks(option.checks, data),
        },
      }))
    },
    [option, question.id, response, setFormResponse],
  )

  const setResponseOption = useCallback(
    async (action: SetStateAction<FormResponseOption | null>) => {
      const nextOption = typeof action === 'function' ? action(option) : action

      const next = {
        ...formResponse,
        [question.id]: {
          questionId: question.id,
          responseId: nextOption ? nextOption.id : null,
          data: getInitialFormQuestionResponse(question, mrData),
          comments: getCommentsFromChecks(nextOption?.checks ?? [], {}),
        },
      }

      await setFormResponse(next)

      /**
       * FIXME: must pass the updated response to onPatternChange because it may update the form's content. This way, we provide a way so the caller does not override the response change.
       *
       * State management (current via RxDB) must refactored in a way that allows batched updates to the form's content. I.e.: using a Reducer.
       */
      onPatternChange?.(option ? option.pattern : null, next)
    },
    [option, formResponse, setFormResponse, onPatternChange, mrData, question],
  )

  const responseContext = useMemo(
    (): StateDuple<Json> => [response, setResponse],
    [response, setResponse],
  )

  const optionContext = useMemo(
    (): StateDuple<FormResponseOption | null> => [option, setResponseOption],
    [option, setResponseOption],
  )

  return (
    <FormQuestionSpecContext.Provider value={question}>
      <FormQuestionResponseContext.Provider value={responseContext}>
        <FormResponseOptionContext.Provider value={optionContext}>
          {children}
        </FormResponseOptionContext.Provider>
      </FormQuestionResponseContext.Provider>
    </FormQuestionSpecContext.Provider>
  )
}

function useFormQuestionResponse<ResponseType extends Json>() {
  const context = use(FormQuestionResponseContext)

  invariant(context, 'Must be used within a provider')

  return [
    context[0] as ResponseType,
    context[1] as (data: SetStateAction<ResponseType>) => Promise<void>,
  ] as const
}
function useFormQuestionResponseOption() {
  const context = use(FormResponseOptionContext)

  invariant(context, 'Must be used within a provider')

  return context
}

function useFormQuestionMRData() {
  const question = useFormQuestion()
  const mrData = useFormMRData()

  return mrData[question.id]
}

function useFormQuestion(): FormQuestion {
  const question = use(FormQuestionSpecContext)

  invariant(question, 'Must be used within a provider')

  return question
}

function usePatternSchema(): ZodSchema {
  const question = useFormQuestion()
  return useFormQuestionPatternSchema(question)
}

function useResponseRows(fallback: Json[] = [{}]) {
  const [response, setResponse] = useFormQuestionResponse()
  const mrData = useFormQuestionMRData()

  const rows = Array.isArray(response) ? response : fallback

  return {
    array: rows,
    add: () => {
      const newResponse = [...rows, Object.assign({}, mrData)]
      setResponse(newResponse).catch(console.error)
    },
    remove: (index: number) => {
      const newResponse = rows.filter((_, i) => i !== index)
      setResponse(newResponse).catch(console.error)
    },
  }
}

export default FormQuestionProvider

export {
  useFormQuestion,
  useFormQuestionMRData,
  useFormQuestionResponse,
  useFormQuestionResponseOption,
  usePatternSchema,
  useResponseRows,
}
