import { <PERSON><PERSON> } from '@/types/Json'
import { createContext, PropsWithChildren, SetStateAction, use } from 'react'
import { ZodSchema } from 'zod'
import {
  FormContent,
  FormQuestion,
  FormResponseOption,
  getCommentsFromChecks,
  getInitialData,
  getSelectedResponseOption,
} from '../types/Form'
import { FormResponsePattern } from '../types/FormResponsePattern'
import {
  useFormMRAttributes,
  useFormQuestionPatternSchema,
  useFormResponse,
} from './FormProvider'

type FormQuestionContext = {
  /** The specification of the question */
  question: FormQuestion

  /** The selected response option */
  option: FormResponseOption | null

  /**
   * Holds the response to the question given the selected response option.
   */
  response: Json

  /**
   * Takes the questionId, responseId, and the data to set the response to the question.
   *
   * @param data the data object to set as the response to the question.
   * @returns
   */
  setResponse: (data: SetStateAction<Json>) => Promise<void>

  /**
   * Takes the questionId, responseId, and the response option ID for the question.
   *
   * @param data the data object to set as the response to the question.
   * @returns
   */
  setResponseOption: (option: FormResponseOption) => Promise<void>
}

const FormQuestionContext = createContext<FormQuestionContext | undefined>(
  undefined,
)

type Props = PropsWithChildren<{
  question: FormQuestion
  /**
   * FIXME: onPatternChange should not expect the form's response. See comment in usage below.
   */
  onPatternChange?: (p: FormResponsePattern, r: FormContent['response']) => void
}>

const FormQuestionProvider = ({
  children,
  question,
  onPatternChange,
}: Props) => {
  const [formResponse, setFormResponse] = useFormResponse()
  const option = getSelectedResponseOption(question, formResponse)
  const mrAttributes = useFormMRAttributes()
  const response = formResponse[question.id].data ?? {}

  async function setResponse(action: SetStateAction<Json>) {
    if (!option) {
      throw new Error('No response option selected')
    }

    const data = typeof action === 'function' ? action(response) : action

    await setFormResponse((prev) => ({
      ...prev,
      [question.id]: {
        questionId: question.id,
        responseId: option.id,
        data,
        comments: getCommentsFromChecks(option.checks, data),
      },
    }))
  }

  async function setResponseOption(option: FormResponseOption) {
    const next = {
      ...formResponse,
      [question.id]: {
        questionId: question.id,
        responseId: option.id,
        data: getInitialData(question, mrAttributes ?? []),
        comments: getCommentsFromChecks(option.checks, {}),
      },
    }

    await setFormResponse(next)

    /**
     * FIXME: must pass the updated response to onPatternChange because it may update the form's content. This way, we provide a way so the caller does not override the response change.
     *
     * State management (current via RxDB) must refactored in a way that allows batched updates to the form's content. I.e.: using a Reducer.
     */
    onPatternChange?.(option.pattern, next)
  }

  return (
    <FormQuestionContext.Provider
      value={{
        question,
        option,
        response,
        setResponse,
        setResponseOption,
      }}
    >
      {children}
    </FormQuestionContext.Provider>
  )
}

function useFormQuestionContext(): FormQuestionContext {
  const context = use(FormQuestionContext)

  if (!context) {
    throw new Error(
      'useFormQuestionContext must be used within a FormQuestionProvider',
    )
  }

  return context
}

function useFormQuestionResponse<ResponseType extends Json>() {
  const { response, setResponse } = useFormQuestionContext()

  return [response as ResponseType, setResponse] as const
}

function useFormQuestionResponseOption() {
  const { option, setResponseOption } = useFormQuestionContext()
  return [option, setResponseOption] as const
}

function useFormQuestion(): FormQuestion {
  return useFormQuestionContext().question
}

function useFormResponseOption(): FormResponseOption | null {
  return useFormQuestionContext().option
}

function usePatternSchema(): ZodSchema {
  const question = useFormQuestion()
  return useFormQuestionPatternSchema(question)
}

function useResponseRows(fallback: Json[] = [{}]) {
  const [response, setResponse] = useFormQuestionResponse()

  const rows = Array.isArray(response) ? response : fallback

  return {
    array: rows,
    add: () => {
      const newResponse = [...rows, {}]
      setResponse(newResponse).catch(console.error)
    },
    remove: (index: number) => {
      const newResponse = rows.filter((_, i) => i !== index)
      setResponse(newResponse).catch(console.error)
    },
  }
}

export default FormQuestionProvider

export {
  useFormQuestion,
  useFormQuestionResponse,
  useFormQuestionResponseOption,
  useFormResponseOption,
  usePatternSchema,
  useResponseRows,
}
