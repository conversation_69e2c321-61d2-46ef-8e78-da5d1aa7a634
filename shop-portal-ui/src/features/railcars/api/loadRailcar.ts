'use server'

import { ActionConfig, remote } from '@/app/actions'
import { cacheFor, getCachingClient } from '@/utils/axios'
import dayjs from 'dayjs'
import type { Railcar, ServiceEventSummary } from '../types/Railcar'

export const loadRailcar = remote(async function loadRailcar(
  carNumber: string,
  config: ActionConfig,
) {
  const response = await getCachingClient('railcars').get<Railcar>(
    `/railcars/${carNumber}`,
    { ...config, cache: cacheFor(5, 'minutes') },
  )

  /**
   * A brand-new service event may not yet have a inbound date, but we need an inbound date in order to
   * sort the service events from most to least current. This function determines a good inbound date to
   * use if one is not provided:
   * - Actual inbound date if it exists
   * - Or: actual outbound date if it exists
   * - Or: today's date.
   *
   * Note: THIS IS ONLY FOR SORTING THE SERVICE EVENTS! The null date is not replaced in the returned data (nor
   * should it)
   */
  const determineInboundDate = (
    serviceEventSummary: ServiceEventSummary,
  ): string => {
    const { inboundDate, closureDate } = serviceEventSummary

    return inboundDate ?? closureDate ?? dayjs().format('YYYY-MM-DD')
  }

  return {
    ...response.data,
    serviceEvents: response.data.serviceEvents.sort((a, b) => {
      return determineInboundDate(b).localeCompare(determineInboundDate(a))
    }),
  } as Railcar
})
