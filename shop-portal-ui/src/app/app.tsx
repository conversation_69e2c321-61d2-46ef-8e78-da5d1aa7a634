'use client'

import { useR<PERSON>entAccessHistory } from '@/app/hooks/useRecentAccessHistory'
import ConnectivityBanner from '@/features/connectivity/components/ConnectivityBanner'
import { DataConflictAlertProvider } from '@/features/forms/components/DataConflictModal'
import { useFormList } from '@/features/forms/hooks/useFormList'
import { useRailcar } from '@/features/railcars/hooks/useRailcar'
import { useServiceEvent } from '@/features/service-events/hooks/useServiceEvent'
import { AppProvider, useAppContext } from '@/providers/AppProvider'

import { TabContextProvider } from '@/providers/TabContext'
import createUrl from '@/utils/createUrl'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Landmark } from '@gatx-corp/platform-one-common/components/Content'
import { ModalOverlay } from '@gatx-corp/platform-one-common/components/ModalOverlay'
import { useEffect, useLayoutEffect, useState } from 'react'
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom'

import { FormDraftProvider } from '@/features/forms/providers/FormDraftProvider'
import MRAttributesProvider from '@/providers/MRAttributesProvider'
import SidePanelLayout from './components/SidePanelLayout'
import { AppHeader } from './pages/Header/AppHeader'
import { MainHeader } from './pages/Header/MainHeader'
import { MainTabs } from './pages/MainTabs'

const ServiceEventTracker = () => {
  const { carNumber, serviceEventId } = useAppContext()
  const { pathname } = useLocation()
  const [searchParams] = useSearchParams()
  const { data: railcar } = useRailcar({ carNumber })
  const { register } = useRecentAccessHistory()

  useEffect(() => {
    if (railcar && pathname.includes(railcar.carNumber)) {
      const serviceEvent = railcar.serviceEvents.find(
        (serviceEvent) => serviceEvent.id === serviceEventId,
      )
      if (!serviceEvent) return

      const url = `${pathname}${searchParams ? `?${searchParams.toString()}` : ''}`

      register({
        carNumber: railcar.carNumber,
        carType: railcar.carType,
        serviceEventDate: serviceEvent.closureDate,
        serviceEventId: serviceEvent.id,
        shopCode: serviceEvent.shop,
        returnUrl: url,
      })
    }
  }, [serviceEventId, pathname, railcar, register, searchParams])

  return null
}

const App = () => {
  const { t } = useI18n()

  const [loadStartTime, setLoadStartTime] = useState<number | undefined>()
  const { carNumber, serviceEventId: serviceEventIdParam, formId } = useParams()

  const [searchParams] = useSearchParams()

  const navigate = useNavigate()

  const { data: railcar, isLoading: isLoadingRailcar } = useRailcar({
    carNumber: carNumber!,
  })

  const serviceEventId = serviceEventIdParam
    ? (railcar?.serviceEvents.find((e) => e.id === serviceEventIdParam)?.id ??
      '')
    : (railcar?.serviceEvents[0]?.id ?? '')

  const { data: serviceEvent, isLoading: isLoadingServiceEvent } =
    useServiceEvent({
      serviceEventId: serviceEventId,
      isEnabled: !!serviceEventId,
    })

  const { data: forms, isLoading: isLoadingForms } = useFormList({
    serviceEventId: serviceEventId,
    isEnabled: !!serviceEvent,
  })

  const isFormNotFound =
    serviceEventId &&
    !isLoadingServiceEvent &&
    !isLoadingForms &&
    formId &&
    forms &&
    !forms.find((form) => form.id === formId)

  const currentTab = searchParams.get('tab') ?? undefined

  useLayoutEffect(() => {
    if (carNumber && serviceEventId && isFormNotFound) {
      navigate(createUrl({ carNumber, serviceEventId }, { tab: currentTab }))
    }
  }, [isFormNotFound, carNumber, serviceEventId, navigate, currentTab])

  useEffect(() => {
    if ((isLoadingRailcar || isLoadingServiceEvent) && !loadStartTime) {
      setLoadStartTime(performance.now())
    } else if (loadStartTime) {
      const loadElapsedTimeSeconds = (performance.now() - loadStartTime) / 1000

      console.debug(
        `[${loadElapsedTimeSeconds}] - Main Loading: isLoadingRailcar: ${isLoadingRailcar}, isLoadingServiceEvent: ${isLoadingServiceEvent}`,
      )

      if (!isLoadingRailcar && !isLoadingServiceEvent) {
        setLoadStartTime(undefined)
      }
    }
  }, [isLoadingRailcar, isLoadingServiceEvent, loadStartTime])

  return (
    <AppProvider
      serviceEventId={serviceEventId}
      shopCode={serviceEvent?.shop ?? ''}
      carNumber={carNumber}
      formId={isFormNotFound ? undefined : formId}
    >
      <MRAttributesProvider>
        <FormDraftProvider>
          <DataConflictAlertProvider>
            <ServiceEventTracker />
            <TabContextProvider>
              <SidePanelLayout disabled={!serviceEvent}>
                <div className="flex flex-col min-h-screen pb-xl">
                  <ConnectivityBanner />
                  <AppHeader alerts={serviceEvent?.alerts} />
                  <Landmark as="main" className="flex flex-col flex-1">
                    <MainHeader />
                    <ModalOverlay
                      show={isLoadingRailcar}
                      showSpinner={isLoadingRailcar}
                      title={t('loading')}
                    >
                      <h1 tabIndex={0}>{t('loading')}</h1>
                    </ModalOverlay>

                    {railcar && <MainTabs railcar={railcar} />}
                  </Landmark>
                </div>
              </SidePanelLayout>
            </TabContextProvider>
          </DataConflictAlertProvider>
        </FormDraftProvider>
      </MRAttributesProvider>
    </AppProvider>
  )
}

export default App
