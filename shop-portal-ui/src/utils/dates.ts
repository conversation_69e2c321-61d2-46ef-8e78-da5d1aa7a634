import dayjs, { OpUnitType } from 'dayjs'

export const DATE_FORMAT = {
  VERBOSE: 'MM/DD/YYYY',
  VERBOSE_WITH_TIME: 'MM/DD/YYYY HH:mm',
} as const

export const formatDate = (date?: string) => {
  if (!date) return ''
  return dayjs(date).format(DATE_FORMAT.VERBOSE)
}

export const formatDateAndTime = (date?: string) => {
  if (!date) return ''

  return dayjs(date).format(DATE_FORMAT.VERBOSE_WITH_TIME)
}

export const isDateAfter = (
  date?: string,
  other?: string,
  opts: { unit?: OpUnitType } = {},
) => {
  return date && other && dayjs(date).isAfter(dayjs(other), opts.unit)
}

export const hasISOFormat = (date: string) => {
  return /^\d{4}-\d{2}-\d{2}$/.test(date)
}

export const toCalendarDate = (date: string) => {
  if (hasISOFormat(date)) {
    return date
  }

  const [month, day, year] = date.split('/')
  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
}
